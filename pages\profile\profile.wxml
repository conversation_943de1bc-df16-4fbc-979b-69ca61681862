<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info" bindtap="login" wx:if="{{!userInfo}}">
      <image class="user-avatar" src="/images/default-avatar.png"></image>
      <view class="user-details">
        <text class="user-name">点击登录</text>
        <text class="user-desc">登录后可同步下载记录</text>
      </view>
      <image class="arrow-icon" src="/images/arrow-right.png"></image>
    </view>
    
    <view class="user-info" wx:else>
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName}}</text>
        <text class="user-desc">已登录 · VIP用户</text>
      </view>
      <image class="arrow-icon" src="/images/arrow-right.png"></image>
    </view>

    <!-- 用户统计 -->
    <view class="user-stats">
      <view class="stats-item" bindtap="goToReading">
        <text class="stats-number">{{userStats.reading}}</text>
        <text class="stats-label">阅读数</text>
      </view>
      <view class="stats-item" bindtap="goToFavorites">
        <text class="stats-number">{{userStats.favorites}}</text>
        <text class="stats-label">收藏数</text>
      </view>
      <view class="stats-item" bindtap="goToHistory">
        <text class="stats-number">{{userStats.history}}</text>
        <text class="stats-label">浏览数</text>
      </view>
      <view class="stats-item" bindtap="goToComments">
        <text class="stats-number">{{userStats.comments}}</text>
        <text class="stats-label">评论数</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToFavorites">
        <view class="menu-left">
          <image class="menu-icon" src="/images/heart.png"></image>
          <text class="menu-title">我的收藏</text>
        </view>
        <view class="menu-right">
          <text class="menu-badge" wx:if="{{userStats.favorites > 0}}">{{userStats.favorites}}</text>
          <image class="arrow-icon" src="/images/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToHistory">
        <view class="menu-left">
          <image class="menu-icon" src="/images/history.png"></image>
          <text class="menu-title">浏览历史</text>
        </view>
        <view class="menu-right">
          <image class="arrow-icon" src="/images/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToComments">
        <view class="menu-left">
          <image class="menu-icon" src="/images/comment.png"></image>
          <text class="menu-title">我的评论</text>
        </view>
        <view class="menu-right">
          <image class="arrow-icon" src="/images/arrow-right.png"></image>
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToSettings">
        <view class="menu-left">
          <image class="menu-icon" src="/images/settings.png"></image>
          <text class="menu-title">设置</text>
        </view>
        <view class="menu-right">
          <image class="arrow-icon" src="/images/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToHelp">
        <view class="menu-left">
          <image class="menu-icon" src="/images/help.png"></image>
          <text class="menu-title">帮助与反馈</text>
        </view>
        <view class="menu-right">
          <image class="arrow-icon" src="/images/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToAbout">
        <view class="menu-left">
          <image class="menu-icon" src="/images/info.png"></image>
          <text class="menu-title">关于我们</text>
        </view>
        <view class="menu-right">
          <text class="menu-version">v1.0.0</text>
          <image class="arrow-icon" src="/images/arrow-right.png"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- VIP会员区域 -->
  <view class="vip-section" wx:if="{{userInfo && !userInfo.isVip}}">
    <view class="vip-card">
      <view class="vip-content">
        <view class="vip-title">升级VIP会员</view>
        <view class="vip-desc">享受更快下载速度，无限制下载</view>
        <view class="vip-features">
          <text class="vip-feature">• 高速下载通道</text>
          <text class="vip-feature">• 无下载次数限制</text>
          <text class="vip-feature">• 专属客服支持</text>
        </view>
      </view>
      <button class="vip-btn" bindtap="upgradeVip">
        <text>立即升级</text>
      </button>
    </view>
  </view>

  <!-- 阅读统计 -->
  <view class="reading-section">
    <view class="reading-header">
      <text class="reading-title">阅读统计</text>
      <text class="reading-detail" bindtap="goToReading">查看详情</text>
    </view>
    <view class="reading-info">
      <view class="reading-item">
        <text class="reading-label">本周阅读</text>
        <text class="reading-value">{{readingStats.weekly}}篇</text>
      </view>
      <view class="reading-item">
        <text class="reading-label">阅读时长</text>
        <text class="reading-value">{{readingStats.duration}}</text>
      </view>
      <view class="reading-item">
        <text class="reading-label">最爱分类</text>
        <text class="reading-value">{{readingStats.favoriteCategory}}</text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo}}">
    <button class="logout-btn" bindtap="logout">
      <text>退出登录</text>
    </button>
  </view>
</view>
