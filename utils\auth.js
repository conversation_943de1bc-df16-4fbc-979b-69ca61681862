// utils/auth.js - 用户认证工具类
const apiManager = require('./api.js')
const storageManager = require('./storage.js')

class AuthManager {
  constructor() {
    this.userInfo = null
    this.isLoggedIn = false
  }

  // 微信登录
  async wxLogin() {
    try {
      // 获取微信登录code
      const loginRes = await this.getWxLoginCode()
      
      // 调用后端登录接口
      const loginData = await apiManager.user.login(loginRes.code)
      
      // 保存用户信息和token
      this.userInfo = loginData.userInfo
      this.isLoggedIn = true
      
      // 存储到本地
      storageManager.set('userToken', loginData.token)
      storageManager.set('userInfo', loginData.userInfo)
      
      return loginData
    } catch (error) {
      console.error('微信登录失败:', error)
      throw error
    }
  }

  // 获取微信登录code
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  }

  // 获取用户信息
  async getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo)
        },
        fail: reject
      })
    })
  }

  // 检查登录状态
  checkLoginStatus() {
    const token = storageManager.get('userToken')
    const userInfo = storageManager.get('userInfo')
    
    if (token && userInfo) {
      this.userInfo = userInfo
      this.isLoggedIn = true
      return true
    }
    
    return false
  }

  // 获取当前用户信息
  getCurrentUser() {
    if (!this.isLoggedIn) {
      this.checkLoginStatus()
    }
    return this.userInfo
  }

  // 更新用户信息
  async updateUserInfo(newUserInfo) {
    try {
      const updatedInfo = await apiManager.user.updateUserInfo(newUserInfo)
      
      this.userInfo = { ...this.userInfo, ...updatedInfo }
      storageManager.set('userInfo', this.userInfo)
      
      return this.userInfo
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }

  // 退出登录
  logout() {
    this.userInfo = null
    this.isLoggedIn = false
    
    // 清除本地存储
    storageManager.remove('userToken')
    storageManager.remove('userInfo')
    
    // 可以选择清除其他用户相关数据
    // storageManager.remove('favorites')
    // storageManager.remove('history')
  }

  // 检查是否需要重新登录
  async checkTokenValid() {
    try {
      await apiManager.user.getUserInfo()
      return true
    } catch (error) {
      // token无效，清除登录状态
      this.logout()
      return false
    }
  }

  // 静默登录（应用启动时调用）
  async silentLogin() {
    // 检查本地是否有登录信息
    if (this.checkLoginStatus()) {
      // 验证token是否有效
      const isValid = await this.checkTokenValid()
      if (isValid) {
        return this.userInfo
      }
    }
    
    // 尝试微信静默登录
    try {
      return await this.wxLogin()
    } catch (error) {
      console.log('静默登录失败:', error)
      return null
    }
  }

  // 强制登录（需要用户授权）
  async forceLogin() {
    try {
      // 先获取用户信息授权
      const userProfile = await this.getUserProfile()
      
      // 然后进行微信登录
      const loginData = await this.wxLogin()
      
      // 更新用户信息
      if (userProfile) {
        await this.updateUserInfo({
          nickName: userProfile.nickName,
          avatarUrl: userProfile.avatarUrl,
          gender: userProfile.gender,
          country: userProfile.country,
          province: userProfile.province,
          city: userProfile.city
        })
      }
      
      return this.userInfo
    } catch (error) {
      console.error('强制登录失败:', error)
      throw error
    }
  }

  // 检查是否为VIP用户
  isVipUser() {
    return this.userInfo && this.userInfo.isVip
  }

  // 获取用户权限
  getUserPermissions() {
    if (!this.userInfo) return []
    
    const permissions = ['basic']
    
    if (this.isVipUser()) {
      permissions.push('vip', 'unlimited_download', 'high_speed')
    }
    
    return permissions
  }

  // 检查用户是否有特定权限
  hasPermission(permission) {
    const permissions = this.getUserPermissions()
    return permissions.includes(permission)
  }

  // 获取用户统计信息
  async getUserStats() {
    try {
      if (!this.isLoggedIn) {
        return {
          downloads: 0,
          favorites: 0,
          history: 0,
          comments: 0
        }
      }
      
      const stats = await apiManager.stats.getUserStats()
      return stats
    } catch (error) {
      console.error('获取用户统计失败:', error)
      
      // 返回本地统计
      const favorites = storageManager.getFavorites()
      const history = storageManager.getHistory()
      const downloadRecords = storageManager.getDownloadRecords()
      
      return {
        downloads: downloadRecords.length,
        favorites: favorites.length,
        history: history.length,
        comments: 0
      }
    }
  }

  // 同步本地数据到服务器
  async syncLocalData() {
    if (!this.isLoggedIn) return
    
    try {
      // 同步收藏
      const favorites = storageManager.getFavorites()
      if (favorites.length > 0) {
        // 调用API同步收藏数据
        console.log('同步收藏数据:', favorites.length)
      }
      
      // 同步下载记录
      const downloadRecords = storageManager.getDownloadRecords()
      if (downloadRecords.length > 0) {
        // 调用API同步下载记录
        console.log('同步下载记录:', downloadRecords.length)
      }
      
      // 同步浏览历史
      const history = storageManager.getHistory()
      if (history.length > 0) {
        // 调用API同步浏览历史
        console.log('同步浏览历史:', history.length)
      }
      
    } catch (error) {
      console.error('同步本地数据失败:', error)
    }
  }

  // 从服务器拉取用户数据
  async pullUserData() {
    if (!this.isLoggedIn) return
    
    try {
      // 拉取收藏数据
      const favorites = await apiManager.favorite.getList()
      if (favorites && favorites.length > 0) {
        // 合并到本地收藏
        favorites.forEach(item => {
          storageManager.addFavorite(item.id, item)
        })
      }
      
      // 拉取下载历史
      const downloadHistory = await apiManager.download.getHistory()
      if (downloadHistory && downloadHistory.length > 0) {
        // 更新本地下载记录
        console.log('拉取下载历史:', downloadHistory.length)
      }
      
    } catch (error) {
      console.error('拉取用户数据失败:', error)
    }
  }
}

// 创建全局认证管理器实例
const authManager = new AuthManager()

module.exports = authManager
