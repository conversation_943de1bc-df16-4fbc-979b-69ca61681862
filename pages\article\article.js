// pages/article/article.js
const app = getApp()
const storageManager = require('../../utils/storage.js')

Page({
  data: {
    article: {},
    relatedArticles: [],
    comments: [],
    commentText: '',
    showSettingsModal: false,
    theme: 'light',
    fontSize: 16,
    lineHeight: 1.6,
    fontFamily: 'system'
  },

  onLoad(options) {
    const id = options.id
    if (id) {
      this.loadArticle(id)
      this.loadRelatedArticles(id)
      this.loadComments(id)
    }
    
    // 加载阅读设置
    this.loadReadingSettings()
  },

  onShow() {
    // 记录阅读历史
    if (this.data.article.id) {
      storageManager.addHistory(this.data.article.id, this.data.article)
    }
  },

  // 加载文章详情
  loadArticle(id) {
    // 模拟API请求
    const mockArticle = {
      id: parseInt(id),
      title: '人工智能在医疗领域的革命性应用',
      author: '医疗科技专家',
      authorAvatar: '/images/author1.jpg',
      publishTime: '2024-01-15 10:30',
      views: 1520,
      likes: 89,
      comments: 23,
      tags: ['AI', '医疗', '科技', '健康'],
      cover: '/images/article-cover.jpg',
      content: `
        <p>人工智能（AI）技术正在医疗领域掀起一场革命。从诊断到治疗，从药物研发到个性化医疗，AI正在改变着我们对医疗保健的理解和实践。</p>
        
        <h2>AI在医疗诊断中的应用</h2>
        <p>医疗影像诊断是AI应用最成功的领域之一。深度学习算法可以分析X光片、CT扫描、MRI图像等，帮助医生更准确地识别疾病。</p>
        
        <p>例如，Google开发的AI系统在眼科疾病诊断方面已经达到了专业医生的水平，能够准确识别糖尿病视网膜病变。</p>
        
        <h2>药物研发的新突破</h2>
        <p>传统的药物研发周期长达10-15年，成本高昂。AI技术可以大大缩短这个过程，通过分析大量的分子数据，预测药物的效果和副作用。</p>
        
        <p>DeepMind的AlphaFold项目在蛋白质结构预测方面取得了突破性进展，为新药研发提供了重要工具。</p>
        
        <h2>个性化医疗的未来</h2>
        <p>每个人的基因、生活方式、环境都不同，AI可以帮助医生为每个患者制定个性化的治疗方案。</p>
        
        <p>通过分析患者的基因组数据、病史、生活习惯等信息，AI可以预测疾病风险，推荐最适合的治疗方法。</p>
        
        <h2>挑战与机遇</h2>
        <p>尽管AI在医疗领域前景广阔，但也面临着数据隐私、算法透明度、监管等挑战。我们需要在技术创新和安全性之间找到平衡。</p>
        
        <p>未来，AI将成为医生的得力助手，而不是替代者。人机协作将为患者提供更好的医疗服务。</p>
      `,
      isFavorite: false,
      isLiked: false
    }

    this.setData({ article: mockArticle })
    
    // 增加阅读数
    this.increaseViews(id)
  },

  // 加载相关文章
  loadRelatedArticles(id) {
    const relatedArticles = [
      {
        id: 101,
        title: '机器学习在疾病预测中的应用',
        cover: '/images/related1.jpg',
        author: 'AI研究员',
        views: 856
      },
      {
        id: 102,
        title: '医疗大数据的价值与挑战',
        cover: '/images/related2.jpg',
        author: '数据科学家',
        views: 642
      },
      {
        id: 103,
        title: '智能医疗设备的发展趋势',
        cover: '/images/related3.jpg',
        author: '医疗器械专家',
        views: 423
      }
    ]

    this.setData({ relatedArticles })
  },

  // 加载评论
  loadComments(id) {
    const comments = [
      {
        id: 1,
        nickname: '医学生小王',
        avatar: '/images/user1.jpg',
        content: '文章写得很好，AI确实在改变医疗行业，期待更多突破！',
        createTime: '2小时前',
        likes: 5,
        isLiked: false
      },
      {
        id: 2,
        nickname: '科技爱好者',
        avatar: '/images/user2.jpg',
        content: '个性化医疗是未来的趋势，希望能早日普及',
        createTime: '1小时前',
        likes: 3,
        isLiked: false
      }
    ]

    this.setData({ comments })
  },

  // 加载阅读设置
  loadReadingSettings() {
    const settings = app.globalData.readingSettings
    this.setData({
      theme: settings.theme,
      fontSize: settings.fontSize,
      lineHeight: settings.lineHeight,
      fontFamily: settings.fontFamily
    })
  },

  // 保存阅读设置
  saveReadingSettings() {
    const settings = {
      theme: this.data.theme,
      fontSize: this.data.fontSize,
      lineHeight: this.data.lineHeight,
      fontFamily: this.data.fontFamily
    }
    
    app.globalData.readingSettings = settings
    storageManager.set('readingSettings', settings)
  },

  // 增加阅读数
  increaseViews(id) {
    // 这里应该调用API增加阅读数
    console.log('增加文章阅读数:', id)
  },

  // 切换收藏状态
  toggleFavorite() {
    const isFavorite = !this.data.article.isFavorite
    this.setData({
      'article.isFavorite': isFavorite
    })

    if (isFavorite) {
      storageManager.addFavorite(this.data.article.id, this.data.article)
      app.showToast('收藏成功', 'success')
    } else {
      storageManager.removeFavorite(this.data.article.id)
      app.showToast('取消收藏', 'success')
    }
  },

  // 分享文章
  shareArticle() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 显示设置
  showSettings() {
    this.setData({ showSettingsModal: true })
  },

  // 隐藏设置
  hideSettings() {
    this.setData({ showSettingsModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 切换点赞状态
  toggleLike() {
    const isLiked = !this.data.article.isLiked
    const likes = this.data.article.likes + (isLiked ? 1 : -1)
    
    this.setData({
      'article.isLiked': isLiked,
      'article.likes': likes
    })

    app.showToast(isLiked ? '点赞成功' : '取消点赞', 'success')
  },

  // 改变主题
  changeTheme(e) {
    const theme = e.currentTarget.dataset.theme
    this.setData({ theme })
    this.saveReadingSettings()
  },

  // 改变字体大小
  changeFontSize(e) {
    const action = e.currentTarget.dataset.action
    let fontSize = this.data.fontSize
    
    if (action === 'increase' && fontSize < 24) {
      fontSize += 2
    } else if (action === 'decrease' && fontSize > 12) {
      fontSize -= 2
    }
    
    this.setData({ fontSize })
    this.saveReadingSettings()
  },

  // 改变行间距
  changeLineHeight(e) {
    const action = e.currentTarget.dataset.action
    let lineHeight = this.data.lineHeight
    
    if (action === 'increase' && lineHeight < 2.5) {
      lineHeight += 0.2
    } else if (action === 'decrease' && lineHeight > 1.0) {
      lineHeight -= 0.2
    }
    
    this.setData({ lineHeight: Math.round(lineHeight * 10) / 10 })
    this.saveReadingSettings()
  },

  // 改变字体
  changeFontFamily(e) {
    const fontFamily = e.currentTarget.dataset.font
    this.setData({ fontFamily })
    this.saveReadingSettings()
  },

  // 评论输入
  onCommentInput(e) {
    this.setData({
      commentText: e.detail.value
    })
  },

  // 提交评论
  submitComment() {
    const content = this.data.commentText.trim()
    if (!content) return

    // 模拟提交评论
    const newComment = {
      id: Date.now(),
      nickname: '我',
      avatar: '/images/default-avatar.jpg',
      content: content,
      createTime: '刚刚',
      likes: 0,
      isLiked: false
    }

    this.setData({
      comments: [newComment, ...this.data.comments],
      commentText: '',
      'article.comments': this.data.article.comments + 1
    })

    app.showToast('评论成功', 'success')
  },

  // 点赞评论
  likeComment(e) {
    const commentId = e.currentTarget.dataset.id
    const comments = this.data.comments.map(comment => {
      if (comment.id === commentId) {
        return {
          ...comment,
          isLiked: !comment.isLiked,
          likes: comment.likes + (comment.isLiked ? -1 : 1)
        }
      }
      return comment
    })

    this.setData({ comments })
  },

  // 回复评论
  replyComment(e) {
    const commentId = e.currentTarget.dataset.id
    // 这里可以实现回复功能
    console.log('回复评论:', commentId)
  },

  // 去文章详情页面（相关推荐）
  goToArticle(e) {
    const id = e.currentTarget.dataset.id
    wx.redirectTo({
      url: `/pages/article/article?id=${id}`
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: this.data.article.title,
      path: `/pages/article/article?id=${this.data.article.id}`,
      imageUrl: this.data.article.cover
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.data.article.title,
      imageUrl: this.data.article.cover
    }
  }
})
