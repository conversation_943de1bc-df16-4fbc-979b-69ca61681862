// app.js
const authManager = require('./utils/auth.js')
const storageManager = require('./utils/storage.js')

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 初始化应用
    this.initApp()
  },

  // 初始化应用
  async initApp() {
    try {
      // 清理过期缓存
      storageManager.clearExpiredCache()

      // 尝试静默登录
      const userInfo = await authManager.silentLogin()
      if (userInfo) {
        this.globalData.userInfo = userInfo
        console.log('自动登录成功:', userInfo.nickName)

        // 同步用户数据
        authManager.syncLocalData()
        authManager.pullUserData()
      }
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  },
  
  globalData: {
    userInfo: null,
    baseUrl: 'https://your-api-domain.com', // 替换为你的后端API地址
    categories: [
      { id: 1, name: '科技前沿', icon: 'tech', color: '#2196F3' },
      { id: 2, name: '生活百科', icon: 'life', color: '#4CAF50' },
      { id: 3, name: '健康养生', icon: 'health', color: '#FF9800' },
      { id: 4, name: '教育学习', icon: 'education', color: '#9C27B0' },
      { id: 5, name: '娱乐八卦', icon: 'entertainment', color: '#E91E63' },
      { id: 6, name: '财经商业', icon: 'business', color: '#607D8B' },
      { id: 7, name: '旅游美食', icon: 'travel', color: '#FF5722' },
      { id: 8, name: '文化艺术', icon: 'culture', color: '#795548' }
    ],
    readingSettings: {
      fontSize: 16,
      lineHeight: 1.6,
      theme: 'light', // light, dark, sepia
      fontFamily: 'system'
    }
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise(async (resolve, reject) => {
      try {
        // 优先从认证管理器获取
        let userInfo = authManager.getCurrentUser()

        if (userInfo) {
          this.globalData.userInfo = userInfo
          resolve(userInfo)
          return
        }

        // 如果没有，尝试强制登录
        userInfo = await authManager.forceLogin()
        this.globalData.userInfo = userInfo
        resolve(userInfo)
      } catch (error) {
        reject(error)
      }
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    return authManager.checkLoginStatus()
  },

  // 退出登录
  logout() {
    authManager.logout()
    this.globalData.userInfo = null
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'content-type': 'application/json',
          ...options.header
        },
        success: res => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: reject
      })
    })
  },

  // 显示提示信息
  showToast(title, icon = 'none') {
    wx.showToast({
      title,
      icon,
      duration: 2000
    })
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    })
  },

  // 隐藏加载
  hideLoading() {
    wx.hideLoading()
  }
})
