// app.js
const authManager = require('./utils/auth.js')
const storageManager = require('./utils/storage.js')

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 初始化应用
    this.initApp()
  },

  // 初始化应用
  async initApp() {
    try {
      // 清理过期缓存
      storageManager.clearExpiredCache()

      // 尝试静默登录
      const userInfo = await authManager.silentLogin()
      if (userInfo) {
        this.globalData.userInfo = userInfo
        console.log('自动登录成功:', userInfo.nickName)

        // 同步用户数据
        authManager.syncLocalData()
        authManager.pullUserData()
      }
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  },
  
  globalData: {
    userInfo: null,
    baseUrl: 'https://your-api-domain.com', // 替换为你的后端API地址
    downloadPath: wx.env.USER_DATA_PATH + '/downloads/', // 下载文件存储路径
    categories: [
      { id: 1, name: '小说文学', icon: 'book', color: '#FF6B6B' },
      { id: 2, name: '学习资料', icon: 'study', color: '#4ECDC4' },
      { id: 3, name: '图片素材', icon: 'image', color: '#45B7D1' },
      { id: 4, name: '音频资源', icon: 'music', color: '#96CEB4' },
      { id: 5, name: '视频教程', icon: 'video', color: '#FFEAA7' },
      { id: 6, name: '软件工具', icon: 'tool', color: '#DDA0DD' }
    ]
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise(async (resolve, reject) => {
      try {
        // 优先从认证管理器获取
        let userInfo = authManager.getCurrentUser()

        if (userInfo) {
          this.globalData.userInfo = userInfo
          resolve(userInfo)
          return
        }

        // 如果没有，尝试强制登录
        userInfo = await authManager.forceLogin()
        this.globalData.userInfo = userInfo
        resolve(userInfo)
      } catch (error) {
        reject(error)
      }
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    return authManager.checkLoginStatus()
  },

  // 退出登录
  logout() {
    authManager.logout()
    this.globalData.userInfo = null
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'content-type': 'application/json',
          ...options.header
        },
        success: res => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: reject
      })
    })
  },

  // 显示提示信息
  showToast(title, icon = 'none') {
    wx.showToast({
      title,
      icon,
      duration: 2000
    })
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    })
  },

  // 隐藏加载
  hideLoading() {
    wx.hideLoading()
  }
})
