<!--pages/category/category.wxml-->
<view class="container">
  <!-- 分类导航 -->
  <view class="category-nav" wx:if="{{!currentCategory}}">
    <view class="nav-title">选择分类</view>
    <view class="category-grid">
      <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="selectCategory" data-id="{{item.id}}">
        <view class="category-icon" style="background-color: {{item.color}}">
          <image src="/images/{{item.icon}}.png"></image>
        </view>
        <text class="category-name">{{item.name}}</text>
        <text class="category-count">{{item.count || 0}}个资源</text>
      </view>
    </view>
  </view>

  <!-- 分类详情 -->
  <view class="category-detail" wx:if="{{currentCategory}}">
    <!-- 分类头部 -->
    <view class="category-header">
      <view class="header-bg" style="background-color: {{currentCategory.color}}">
        <view class="header-content">
          <view class="category-info">
            <view class="category-icon-large" style="background-color: rgba(255,255,255,0.2)">
              <image src="/images/{{currentCategory.icon}}.png"></image>
            </view>
            <view class="category-text">
              <text class="category-title">{{currentCategory.name}}</text>
              <text class="category-desc">共{{resources.length}}个资源</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选和排序 -->
    <view class="filter-bar">
      <view class="filter-item" bindtap="showSortMenu">
        <text>{{sortText}}</text>
        <image class="arrow-icon" src="/images/arrow-down.png"></image>
      </view>
      <view class="filter-item" bindtap="showFilterMenu">
        <text>筛选</text>
        <image class="filter-icon" src="/images/filter.png"></image>
      </view>
    </view>

    <!-- 资源列表 -->
    <view class="resource-list">
      <view class="resource-item" wx:for="{{resources}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <image class="resource-cover" src="{{item.cover}}" mode="aspectFill"></image>
        <view class="resource-content">
          <text class="resource-title">{{item.title}}</text>
          <text class="resource-desc">{{item.description}}</text>
          <view class="resource-meta">
            <view class="meta-item">
              <image class="meta-icon" src="/images/size.png"></image>
              <text>{{item.size}}</text>
            </view>
            <view class="meta-item">
              <image class="meta-icon" src="/images/download.png"></image>
              <text>{{item.downloads}}</text>
            </view>
            <view class="meta-item">
              <image class="meta-icon" src="/images/time.png"></image>
              <text>{{item.createTime}}</text>
            </view>
          </view>
          <view class="resource-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
        <view class="resource-action">
          <button class="download-btn" bindtap="downloadResource" data-id="{{item.id}}" catchtap="true">
            <image src="/images/download-white.png"></image>
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{resources.length === 0 && !loading}}">
      <image class="empty-icon" src="/images/empty.png"></image>
      <text class="empty-text">暂无资源</text>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
      <text wx:if="{{!loading}}">点击加载更多</text>
      <text wx:else>加载中...</text>
    </view>
  </view>

  <!-- 排序菜单 -->
  <view class="sort-menu" wx:if="{{showSort}}" bindtap="hideSortMenu">
    <view class="menu-content" catchtap="stopPropagation">
      <view class="menu-title">排序方式</view>
      <view class="menu-item {{sortType === 'time' ? 'active' : ''}}" bindtap="changeSort" data-type="time">
        <text>按时间排序</text>
        <image wx:if="{{sortType === 'time'}}" src="/images/check.png"></image>
      </view>
      <view class="menu-item {{sortType === 'downloads' ? 'active' : ''}}" bindtap="changeSort" data-type="downloads">
        <text>按下载量排序</text>
        <image wx:if="{{sortType === 'downloads'}}" src="/images/check.png"></image>
      </view>
      <view class="menu-item {{sortType === 'size' ? 'active' : ''}}" bindtap="changeSort" data-type="size">
        <text>按大小排序</text>
        <image wx:if="{{sortType === 'size'}}" src="/images/check.png"></image>
      </view>
    </view>
  </view>
</view>
