<!--pages/reading/reading.wxml-->
<view class="container">
  <!-- 顶部统计 -->
  <view class="stats-section">
    <view class="stats-item">
      <text class="stats-number">{{totalReading}}</text>
      <text class="stats-label">总阅读</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{todayReading}}</text>
      <text class="stats-label">今日阅读</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{readingTime}}</text>
      <text class="stats-label">阅读时长</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{favoriteCount}}</text>
      <text class="stats-label">收藏数</text>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-section">
    <view class="action-left">
      <button class="action-btn" bindtap="clearHistory">
        <image src="/images/clear.png"></image>
        <text>清除历史</text>
      </button>
    </view>
    <view class="action-right">
      <button class="action-btn" bindtap="exportData">
        <image src="/images/export.png"></image>
        <text>导出数据</text>
      </button>
    </view>
  </view>

  <!-- 阅读记录 -->
  <view class="reading-list">
    <!-- 正在阅读 -->
    <view class="section-header" wx:if="{{currentReading.length > 0}}">
      <text class="section-title">正在阅读 ({{currentReading.length}})</text>
    </view>
    <view class="reading-item current" wx:for="{{currentReading}}" wx:key="id">
      <image class="item-cover" src="{{item.cover}}" mode="aspectFill"></image>
      <view class="item-content">
        <text class="item-title">{{item.title}}</text>
        <text class="item-info">阅读进度: {{item.progress}}%</text>
        <view class="progress-container">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%"></view>
          </view>
          <text class="progress-text">{{item.progress}}%</text>
        </view>
        <view class="item-meta">
          <text class="reading-time">阅读时长: {{item.readingTime}}</text>
          <text class="last-read">上次阅读: {{item.lastReadTime}}</text>
        </view>
      </view>
      <view class="item-actions">
        <button class="action-icon-btn" bindtap="continueReading" data-id="{{item.id}}">
          <image src="/images/play.png"></image>
        </button>
        <button class="action-icon-btn" bindtap="removeFromCurrent" data-id="{{item.id}}">
          <image src="/images/close.png"></image>
        </button>
      </view>
    </view>

    <!-- 阅读历史 -->
    <view class="section-header" wx:if="{{readingHistory.length > 0}}">
      <text class="section-title">阅读历史 ({{readingHistory.length}})</text>
      <text class="section-action" bindtap="toggleHistory">{{showHistory ? '收起' : '展开'}}</text>
    </view>
    <view class="reading-item history" wx:for="{{readingHistory}}" wx:key="id" wx:if="{{showHistory}}">
      <image class="item-cover" src="{{item.cover}}" mode="aspectFill"></image>
      <view class="item-content">
        <text class="item-title">{{item.title}}</text>
        <text class="item-info">{{item.author}} · {{item.categoryName}}</text>
        <view class="item-meta">
          <text class="read-count">阅读{{item.readCount}}次</text>
          <text class="last-read">{{item.lastReadTime}}</text>
        </view>
      </view>
      <view class="item-actions">
        <button class="action-icon-btn" bindtap="readAgain" data-id="{{item.id}}">
          <image src="/images/read.png"></image>
        </button>
        <button class="action-icon-btn" bindtap="removeFromHistory" data-id="{{item.id}}">
          <image src="/images/delete.png"></image>
        </button>
      </view>
    </view>

    <!-- 收藏文章 -->
    <view class="section-header" wx:if="{{favoriteArticles.length > 0}}">
      <text class="section-title">我的收藏 ({{favoriteArticles.length}})</text>
      <text class="section-action" bindtap="toggleFavorites">{{showFavorites ? '收起' : '展开'}}</text>
    </view>
    <view class="reading-item favorite" wx:for="{{favoriteArticles}}" wx:key="id" wx:if="{{showFavorites}}">
      <image class="item-cover" src="{{item.cover}}" mode="aspectFill"></image>
      <view class="item-content">
        <text class="item-title">{{item.title}}</text>
        <text class="item-info">{{item.author}} · {{item.categoryName}}</text>
        <view class="item-meta">
          <text class="favorite-time">收藏于 {{item.favoriteTime}}</text>
          <text class="article-views">{{item.views}}阅读</text>
        </view>
      </view>
      <view class="item-actions">
        <button class="action-icon-btn" bindtap="readFavorite" data-id="{{item.id}}">
          <image src="/images/read.png"></image>
        </button>
        <button class="action-icon-btn" bindtap="removeFavorite" data-id="{{item.id}}">
          <image src="/images/heart-fill.png"></image>
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{totalReading === 0}}">
    <image class="empty-icon" src="/images/reading-empty.png"></image>
    <text class="empty-title">还没有阅读记录</text>
    <text class="empty-desc">去首页找些有趣的文章阅读吧</text>
    <button class="empty-btn" bindtap="goToHome">
      <text>去首页看看</text>
    </button>
  </view>

  <!-- 阅读统计 -->
  <view class="reading-stats" wx:if="{{totalReading > 0}}">
    <view class="stats-item">
      <text class="stats-label">本周阅读</text>
      <text class="stats-value">{{weeklyReading}}篇</text>
    </view>
    <view class="stats-item">
      <text class="stats-label">平均阅读时长</text>
      <text class="stats-value">{{avgReadingTime}}</text>
    </view>
    <view class="stats-item">
      <text class="stats-label">最喜欢的分类</text>
      <text class="stats-value">{{favoriteCategory}}</text>
    </view>
  </view>
</view>
