/* pages/search/search.wxss */

/* 搜索头部 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-cancel {
  font-size: 26rpx;
  color: #999;
  margin-left: 20rpx;
}

.cancel-btn {
  font-size: 28rpx;
  color: #1976D2;
}

/* 搜索建议 */
.search-suggestions {
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 25rpx;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333;
}

/* 热门搜索 */
.hot-search {
  background-color: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
}

.hot-tag {
  background-color: #f5f5f5;
  color: #666;
  padding: 15rpx 30rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

/* 搜索历史 */
.search-history {
  background-color: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.clear-history {
  font-size: 26rpx;
  color: #999;
}

.history-list {
  display: flex;
  flex-direction: column;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.history-item:last-child {
  border-bottom: none;
}

.history-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 25rpx;
}

.history-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.delete-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 搜索结果 */
.search-results {
  background-color: white;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 26rpx;
  color: #666;
}

.filter-btn {
  display: flex;
  align-items: center;
  color: #1976D2;
}

.filter-btn image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.result-list {
  padding: 0 20rpx;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.result-item:last-child {
  border-bottom: none;
}

.result-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 30rpx;
}

.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-summary {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.result-meta {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.result-category {
  background-color: #e8f5e8;
  color: #2E7D32;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  margin-right: 15rpx;
}

.result-author,
.result-views {
  font-size: 22rpx;
  color: #999;
  margin-right: 15rpx;
}

.result-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  background-color: #f5f5f5;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 5rpx;
}

.read-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #2E7D32;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  border: none;
}

.read-btn image {
  width: 30rpx;
  height: 30rpx;
}

.read-btn::after {
  border: none;
}

/* 无结果 */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
}

.no-results-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.no-results-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.no-results-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.browse-btn {
  background-color: #1976D2;
  color: white;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
  border: none;
}

.browse-btn::after {
  border: none;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.filter-content {
  width: 100%;
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-close {
  width: 40rpx;
  height: 40rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  background-color: #f5f5f5;
  color: #666;
  padding: 15rpx 30rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.filter-option.active {
  background-color: #1976D2;
  color: white;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.filter-reset,
.filter-confirm {
  flex: 1;
  padding: 30rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.filter-reset {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.filter-confirm {
  background-color: #1976D2;
  color: white;
}

.filter-reset::after,
.filter-confirm::after {
  border: none;
}
