// utils/api.js - API接口管理
const app = getApp()

class ApiManager {
  constructor() {
    this.baseUrl = 'https://your-api-domain.com/api' // 替换为实际的API地址
    this.timeout = 10000
  }

  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': this.getAuthToken(),
          ...options.header
        },
        timeout: options.timeout || this.timeout,
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.code === 0) {
              resolve(res.data.data)
            } else {
              reject(new Error(res.data.message || '请求失败'))
            }
          } else {
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (err) => {
          reject(new Error(err.errMsg || '网络请求失败'))
        }
      })
    })
  }

  // 获取认证token
  getAuthToken() {
    // 这里应该从本地存储获取用户token
    return wx.getStorageSync('userToken') || ''
  }

  // GET请求
  get(url, data = {}) {
    return this.request({
      url,
      method: 'GET',
      data
    })
  }

  // POST请求
  post(url, data = {}) {
    return this.request({
      url,
      method: 'POST',
      data
    })
  }

  // PUT请求
  put(url, data = {}) {
    return this.request({
      url,
      method: 'PUT',
      data
    })
  }

  // DELETE请求
  delete(url, data = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data
    })
  }

  // 用户相关API
  user = {
    // 用户登录
    login: (code) => {
      return this.post('/user/login', { code })
    },

    // 获取用户信息
    getUserInfo: () => {
      return this.get('/user/info')
    },

    // 更新用户信息
    updateUserInfo: (userInfo) => {
      return this.put('/user/info', userInfo)
    }
  }

  // 资源相关API
  resource = {
    // 获取资源列表
    getList: (params) => {
      return this.get('/resources', params)
    },

    // 获取资源详情
    getDetail: (id) => {
      return this.get(`/resources/${id}`)
    },

    // 搜索资源
    search: (params) => {
      return this.get('/resources/search', params)
    },

    // 获取热门资源
    getHot: (params) => {
      return this.get('/resources/hot', params)
    },

    // 获取最新资源
    getLatest: (params) => {
      return this.get('/resources/latest', params)
    },

    // 获取推荐资源
    getRecommend: (params) => {
      return this.get('/resources/recommend', params)
    },

    // 获取相关资源
    getRelated: (id) => {
      return this.get(`/resources/${id}/related`)
    }
  }

  // 分类相关API
  category = {
    // 获取分类列表
    getList: () => {
      return this.get('/categories')
    },

    // 获取分类下的资源
    getResources: (id, params) => {
      return this.get(`/categories/${id}/resources`, params)
    }
  }

  // 下载相关API
  download = {
    // 获取下载链接
    getDownloadUrl: (resourceId) => {
      return this.post('/download/url', { resourceId })
    },

    // 记录下载
    recordDownload: (resourceId) => {
      return this.post('/download/record', { resourceId })
    },

    // 获取下载历史
    getHistory: (params) => {
      return this.get('/download/history', params)
    }
  }

  // 收藏相关API
  favorite = {
    // 添加收藏
    add: (resourceId) => {
      return this.post('/favorites', { resourceId })
    },

    // 取消收藏
    remove: (resourceId) => {
      return this.delete(`/favorites/${resourceId}`)
    },

    // 获取收藏列表
    getList: (params) => {
      return this.get('/favorites', params)
    },

    // 检查是否收藏
    check: (resourceId) => {
      return this.get(`/favorites/check/${resourceId}`)
    }
  }

  // 评论相关API
  comment = {
    // 获取评论列表
    getList: (resourceId, params) => {
      return this.get(`/resources/${resourceId}/comments`, params)
    },

    // 添加评论
    add: (resourceId, content) => {
      return this.post(`/resources/${resourceId}/comments`, { content })
    },

    // 删除评论
    delete: (commentId) => {
      return this.delete(`/comments/${commentId}`)
    }
  }

  // 统计相关API
  stats = {
    // 获取用户统计
    getUserStats: () => {
      return this.get('/stats/user')
    },

    // 记录浏览
    recordView: (resourceId) => {
      return this.post('/stats/view', { resourceId })
    }
  }

  // 反馈相关API
  feedback = {
    // 提交反馈
    submit: (content, type = 'general') => {
      return this.post('/feedback', { content, type })
    }
  }

  // 配置相关API
  config = {
    // 获取应用配置
    getAppConfig: () => {
      return this.get('/config/app')
    },

    // 获取轮播图
    getBanners: () => {
      return this.get('/config/banners')
    },

    // 获取热门搜索关键词
    getHotKeywords: () => {
      return this.get('/config/hot-keywords')
    }
  }
}

// 创建全局API管理器实例
const apiManager = new ApiManager()

module.exports = apiManager
