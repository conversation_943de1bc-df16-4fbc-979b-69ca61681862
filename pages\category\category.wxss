/* pages/category/category.wxss */

/* 分类导航 */
.category-nav {
  padding: 40rpx 20rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 48%;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.category-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.category-icon image {
  width: 50rpx;
  height: 50rpx;
}

.category-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.category-count {
  font-size: 24rpx;
  color: #999;
}

/* 分类详情 */
.category-detail {
  min-height: 100vh;
}

.category-header {
  position: relative;
}

.header-bg {
  height: 200rpx;
  position: relative;
}

.header-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
}

.category-info {
  display: flex;
  align-items: center;
}

.category-icon-large {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.category-icon-large image {
  width: 40rpx;
  height: 40rpx;
}

.category-text {
  display: flex;
  flex-direction: column;
}

.category-title {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.category-desc {
  font-size: 26rpx;
  color: rgba(255,255,255,0.8);
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.arrow-icon,
.filter-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

/* 资源列表 */
.resource-list {
  padding: 20rpx;
}

.resource-item {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.resource-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 30rpx;
}

.resource-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.resource-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.resource-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.resource-meta {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  font-size: 22rpx;
  color: #999;
}

.meta-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  background-color: #e3f2fd;
  color: #1976D2;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 5rpx;
}

.resource-action {
  display: flex;
  align-items: center;
}

.download-btn {
  width: 70rpx;
  height: 70rpx;
  background-color: #1976D2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  border: none;
}

.download-btn image {
  width: 35rpx;
  height: 35rpx;
}

.download-btn::after {
  border: none;
}

/* 排序菜单 */
.sort-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.menu-content {
  width: 100%;
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
}

.menu-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #333;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item.active {
  color: #1976D2;
}

.menu-item image {
  width: 32rpx;
  height: 32rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
