/* pages/detail/detail.wxss */

/* 资源头部 */
.resource-header {
  display: flex;
  background-color: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.resource-cover {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  margin-right: 30rpx;
}

.resource-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.resource-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.resource-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.resource-meta {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.meta-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  background-color: #e3f2fd;
  color: #1976D2;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

/* 操作按钮栏 */
.action-bar {
  display: flex;
  background-color: white;
  padding: 30rpx 40rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-right: 20rpx;
  font-size: 24rpx;
  border: none;
}

.action-btn:last-child {
  margin-right: 0;
}

.action-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
  flex: 1;
}

.action-btn.primary {
  background-color: #1976D2;
  color: white;
  flex: 2;
}

.action-btn image {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.action-btn::after {
  border: none;
}

/* 详细信息 */
.detail-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-list {
  padding: 0 40rpx 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 内容预览 */
.preview-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.preview-content {
  padding: 0 40rpx 40rpx;
}

.preview-image {
  width: 100%;
  border-radius: 12rpx;
}

.preview-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  text-indent: 2em;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx;
  color: #999;
}

.preview-placeholder image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

/* 相关推荐 */
.recommend-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.recommend-scroll {
  white-space: nowrap;
  padding: 0 40rpx 40rpx;
}

.recommend-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
}

.recommend-cover {
  width: 100%;
  height: 120rpx;
}

.recommend-info {
  padding: 20rpx;
}

.recommend-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-meta {
  display: flex;
  justify-content: space-between;
}

.recommend-size,
.recommend-downloads {
  font-size: 22rpx;
  color: #999;
}

/* 评论区域 */
.comment-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.comment-count {
  color: #999;
  font-weight: normal;
  margin-left: 10rpx;
}

.comment-input-area {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-input {
  flex: 1;
  background-color: #f8f8f8;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.comment-submit {
  background-color: #1976D2;
  color: white;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  border: none;
}

.comment-submit[disabled] {
  background-color: #ccc;
}

.comment-submit::after {
  border: none;
}

.comment-list {
  padding: 0 40rpx 20rpx;
}

.comment-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.comment-name {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.empty-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx;
  color: #999;
}

.empty-comments image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

/* 下载进度弹窗 */
.download-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
}

.download-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.download-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.download-progress-text {
  font-size: 28rpx;
  color: #1976D2;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background-color: #1976D2;
  transition: width 0.3s ease;
}

.download-speed {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}
