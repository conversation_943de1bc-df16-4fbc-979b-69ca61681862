/* pages/index/wxss (最终完整代码) */
.container { display: flex; flex-direction: column; padding: 20rpx; background-color: #1a1a1a; color: #f0f0f0; height: 100vh; box-sizing: border-box; }
.player-panel, .monster-panel { width: 100%; padding: 20rpx; background-color: #2c2c2c; border-radius: 20rpx; margin-bottom: 20rpx; text-align: center; border: 1px solid #444; flex-shrink: 0; }
.player-name, .monster-name { font-size: 40rpx; font-weight: bold; }
.player-level { font-size: 32rpx; color: #ffd700; margin-top: 10rpx; }
.health-bar-container, .mana-bar-container, .exp-bar-container { margin-top: 15rpx; }
.health-bar-container text, .mana-bar-container text, .exp-bar-container text { font-size: 24rpx; }
progress { margin: 10rpx 0; }
.health-text, .mana-text, .exp-text { font-size: 22rpx; color: #aaa; text-align: right; }
.log-panel { width: 100%; padding: 20rpx; background-color: rgba(0, 0, 0, 0.2); border-radius: 10rpx; border: 1px solid #444; font-size: 28rpx; line-height: 1.6; flex: 1; min-height: 0; overflow-y: auto; display: flex; flex-direction: column-reverse; }
.log-item { margin-bottom: 10rpx; flex-shrink: 0; }
.action-panel { width: 100%; padding-top: 20rpx; flex-shrink: 0; }
.button-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20rpx; }
.action-button { color: white; font-weight: bold; font-size: 28rpx; border-radius: 10rpx; margin: 0 !important; padding: 10rpx 0; }