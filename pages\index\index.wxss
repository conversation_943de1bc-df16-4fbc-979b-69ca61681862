/* pages/index/index.wxss */
.container {
  padding-bottom: 20rpx;
}

/* 搜索区域 */
.search-section {
  padding: 20rpx;
  background-color: #1976D2;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 轮播图区域 */
.banner-section {
  margin: 20rpx;
}

.banner-swiper {
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 分类区域 */
.category-section {
  margin: 40rpx 20rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more-text {
  font-size: 26rpx;
  color: #1976D2;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 22%;
  margin-bottom: 30rpx;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.category-icon image {
  width: 40rpx;
  height: 40rpx;
}

.category-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 推荐区域 */
.recommend-section {
  margin: 40rpx 20rpx;
}

.recommend-scroll {
  white-space: nowrap;
  margin-top: 20rpx;
}

.recommend-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.recommend-image {
  width: 100%;
  height: 120rpx;
}

.recommend-info {
  padding: 20rpx;
}

.recommend-title {
  font-size: 26rpx;
  color: #333;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.recommend-size,
.recommend-downloads {
  font-size: 22rpx;
  color: #999;
}

/* 最新资源区域 */
.latest-section {
  margin: 40rpx 20rpx;
}

.latest-list {
  margin-top: 20rpx;
}

.latest-item {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.latest-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.latest-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.latest-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.latest-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.latest-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.latest-category {
  background-color: #e3f2fd;
  color: #1976D2;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  margin-right: 15rpx;
}

.latest-time,
.latest-size {
  font-size: 22rpx;
  color: #999;
  margin-right: 15rpx;
}

.latest-action {
  display: flex;
  align-items: center;
}

.download-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #1976D2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  border: none;
}

.download-btn image {
  width: 30rpx;
  height: 30rpx;
}

.download-btn::after {
  border: none;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
