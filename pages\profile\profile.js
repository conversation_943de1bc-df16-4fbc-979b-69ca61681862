// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    userStats: {
      downloads: 0,
      favorites: 0,
      history: 0,
      comments: 0
    },
    readingStats: {
      weekly: 0,
      duration: '0分钟',
      favoriteCategory: '暂无'
    }
  },

  onLoad() {
    this.loadUserInfo()
    this.loadUserStats()
    this.loadReadingStats()
  },

  onShow() {
    this.loadUserInfo()
    this.loadUserStats()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo
    this.setData({ userInfo })
  },

  // 加载用户统计
  loadUserStats() {
    // 从存储管理器获取真实数据
    const favorites = storageManager.getFavorites()
    const history = storageManager.getHistory()

    const userStats = {
      reading: history.length + Math.floor(Math.random() * 10), // 阅读数
      favorites: favorites.length,
      history: history.length,
      comments: Math.floor(Math.random() * 10) + 1 // 模拟评论数
    }
    this.setData({ userStats })
  },

  // 加载阅读统计
  loadReadingStats() {
    const history = storageManager.getHistory()
    const favorites = storageManager.getFavorites()

    // 计算本周阅读数（模拟）
    const weeklyReading = Math.floor(Math.random() * 15) + 5

    // 计算阅读时长（模拟）
    const totalMinutes = history.length * 12 + Math.floor(Math.random() * 100)
    const duration = this.formatDuration(totalMinutes)

    // 计算最喜欢的分类
    const categories = history.map(item => item.categoryName).filter(Boolean)
    const favoriteCategory = this.getMostFrequent(categories) || '暂无'

    const readingStats = {
      weekly: weeklyReading,
      duration: duration,
      favoriteCategory: favoriteCategory
    }

    this.setData({ readingStats })
  },

  // 格式化时长
  formatDuration(minutes) {
    if (minutes < 60) {
      return `${minutes}分钟`
    } else {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return `${hours}小时${mins}分钟`
    }
  },

  // 获取最频繁的项目
  getMostFrequent(arr) {
    if (arr.length === 0) return null

    const frequency = {}
    let maxCount = 0
    let mostFrequent = null

    arr.forEach(item => {
      frequency[item] = (frequency[item] || 0) + 1
      if (frequency[item] > maxCount) {
        maxCount = frequency[item]
        mostFrequent = item
      }
    })

    return mostFrequent
  },

  // 用户登录
  login() {
    app.getUserInfo().then(userInfo => {
      this.setData({ userInfo })
      app.showToast('登录成功', 'success')
    }).catch(err => {
      console.error('登录失败', err)
      app.showToast('登录失败', 'error')
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.userInfo = null
          this.setData({ userInfo: null })
          app.showToast('已退出登录', 'success')
        }
      }
    })
  },

  // 去阅读页面
  goToReading() {
    wx.switchTab({
      url: '/pages/reading/reading'
    })
  },

  // 去收藏页面
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    })
  },

  // 去浏览历史页面
  goToHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  // 去评论页面
  goToComments() {
    wx.navigateTo({
      url: '/pages/comments/comments'
    })
  },

  // 去设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 去帮助页面
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  },

  // 去关于页面
  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },

  // 升级VIP
  upgradeVip() {
    wx.showModal({
      title: 'VIP会员',
      content: '升级VIP会员功能正在开发中，敬请期待！',
      showCancel: false
    })
  },



  // 分享给朋友
  onShareAppMessage() {
    return {
      title: '资料下载助手 - 海量资源免费下载',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '资料下载助手 - 海量资源免费下载',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
