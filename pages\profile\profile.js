// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    userStats: {
      downloads: 0,
      favorites: 0,
      history: 0,
      comments: 0
    },
    storageInfo: {
      used: '0MB',
      total: '64GB',
      percent: 0
    }
  },

  onLoad() {
    this.loadUserInfo()
    this.loadUserStats()
    this.loadStorageInfo()
  },

  onShow() {
    this.loadUserInfo()
    this.loadUserStats()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo
    this.setData({ userInfo })
  },

  // 加载用户统计
  loadUserStats() {
    // 模拟用户统计数据
    const userStats = {
      downloads: 15,
      favorites: 8,
      history: 32,
      comments: 5
    }
    this.setData({ userStats })
  },

  // 加载存储信息
  loadStorageInfo() {
    // 模拟存储信息
    const storageInfo = {
      used: '2.3GB',
      total: '64GB',
      percent: 15
    }
    this.setData({ storageInfo })
  },

  // 用户登录
  login() {
    app.getUserInfo().then(userInfo => {
      this.setData({ userInfo })
      app.showToast('登录成功', 'success')
    }).catch(err => {
      console.error('登录失败', err)
      app.showToast('登录失败', 'error')
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.userInfo = null
          this.setData({ userInfo: null })
          app.showToast('已退出登录', 'success')
        }
      }
    })
  },

  // 去下载页面
  goToDownloads() {
    wx.switchTab({
      url: '/pages/download/download'
    })
  },

  // 去收藏页面
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    })
  },

  // 去浏览历史页面
  goToHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  // 去评论页面
  goToComments() {
    wx.navigateTo({
      url: '/pages/comments/comments'
    })
  },

  // 去设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 去帮助页面
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  },

  // 去关于页面
  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },

  // 升级VIP
  upgradeVip() {
    wx.showModal({
      title: 'VIP会员',
      content: '升级VIP会员功能正在开发中，敬请期待！',
      showCancel: false
    })
  },

  // 管理存储空间
  manageStorage() {
    wx.showActionSheet({
      itemList: ['清理缓存', '删除已下载文件', '查看存储详情'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.clearCache()
            break
          case 1:
            this.deleteDownloadedFiles()
            break
          case 2:
            this.showStorageDetail()
            break
        }
      }
    })
  },

  // 清理缓存
  clearCache() {
    wx.showLoading({ title: '清理中...' })
    
    setTimeout(() => {
      wx.hideLoading()
      app.showToast('缓存清理完成', 'success')
      
      // 更新存储信息
      this.setData({
        'storageInfo.used': '1.8GB',
        'storageInfo.percent': 12
      })
    }, 2000)
  },

  // 删除已下载文件
  deleteDownloadedFiles() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除所有已下载的文件吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' })
          
          setTimeout(() => {
            wx.hideLoading()
            app.showToast('文件删除完成', 'success')
            
            // 更新存储信息
            this.setData({
              'storageInfo.used': '0.5GB',
              'storageInfo.percent': 3
            })
          }, 2000)
        }
      }
    })
  },

  // 显示存储详情
  showStorageDetail() {
    wx.showModal({
      title: '存储详情',
      content: `应用数据: 0.5GB\n下载文件: 1.8GB\n缓存文件: 0.3GB\n总计: 2.6GB`,
      showCancel: false
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: '资料下载助手 - 海量资源免费下载',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '资料下载助手 - 海量资源免费下载',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
