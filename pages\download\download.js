// pages/download/download.js
const app = getApp()

Page({
  data: {
    downloadingList: [],
    completedList: [],
    failedList: [],
    showCompleted: true,
    totalDownloads: 0,
    downloadingCount: 0,
    completedCount: 0,
    totalSize: '0MB',
    usedStorage: '0MB',
    totalStorage: '64GB',
    storagePercent: 0
  },

  onLoad() {
    this.loadDownloadList()
    this.startProgressTimer()
  },

  onShow() {
    this.loadDownloadList()
  },

  onUnload() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  },

  // 加载下载列表
  loadDownloadList() {
    // 模拟下载数据
    const downloadingList = [
      {
        id: 1,
        title: '《三体》全集',
        cover: '/images/book1.jpg',
        totalSize: '2.5MB',
        downloadedSize: '1.2MB',
        progress: 48,
        speed: '256KB/s',
        remainingTime: '8秒',
        status: 'downloading'
      },
      {
        id: 2,
        title: 'JavaScript高级教程',
        cover: '/images/book2.jpg',
        totalSize: '15.8MB',
        downloadedSize: '3.2MB',
        progress: 20,
        speed: '0KB/s',
        remainingTime: '已暂停',
        status: 'paused'
      }
    ]

    const completedList = [
      {
        id: 3,
        title: '《流浪地球》',
        cover: '/images/book3.jpg',
        totalSize: '1.8MB',
        completeTime: '2024-01-15 14:30',
        filePath: '/downloads/流浪地球.epub'
      },
      {
        id: 4,
        title: '精美壁纸合集',
        cover: '/images/image1.jpg',
        totalSize: '45.2MB',
        completeTime: '2024-01-14 09:15',
        filePath: '/downloads/wallpapers.zip'
      }
    ]

    const failedList = [
      {
        id: 5,
        title: 'Python入门视频',
        cover: '/images/video1.jpg',
        errorMessage: '网络连接超时'
      }
    ]

    this.setData({
      downloadingList,
      completedList,
      failedList,
      totalDownloads: downloadingList.length + completedList.length + failedList.length,
      downloadingCount: downloadingList.filter(item => item.status === 'downloading').length,
      completedCount: completedList.length,
      totalSize: '65.3MB',
      usedStorage: '65.3MB',
      storagePercent: 15
    })
  },

  // 开始进度更新定时器
  startProgressTimer() {
    this.progressTimer = setInterval(() => {
      const downloadingList = this.data.downloadingList.map(item => {
        if (item.status === 'downloading') {
          let newProgress = item.progress + Math.random() * 5
          if (newProgress >= 100) {
            newProgress = 100
            // 下载完成，移动到已完成列表
            this.moveToCompleted(item.id)
          }
          return {
            ...item,
            progress: Math.floor(newProgress),
            downloadedSize: this.calculateDownloadedSize(item.totalSize, newProgress),
            remainingTime: newProgress < 100 ? `${Math.floor((100 - newProgress) / 5)}秒` : '完成'
          }
        }
        return item
      })

      this.setData({ downloadingList })
    }, 1000)
  },

  // 计算已下载大小
  calculateDownloadedSize(totalSize, progress) {
    const total = parseFloat(totalSize)
    const downloaded = (total * progress / 100).toFixed(1)
    return `${downloaded}${totalSize.slice(-2)}`
  },

  // 移动到已完成列表
  moveToCompleted(id) {
    const item = this.data.downloadingList.find(item => item.id === id)
    if (item) {
      const completedItem = {
        ...item,
        completeTime: new Date().toLocaleString(),
        filePath: `/downloads/${item.title}.${this.getFileExtension(item.title)}`
      }

      this.setData({
        downloadingList: this.data.downloadingList.filter(item => item.id !== id),
        completedList: [completedItem, ...this.data.completedList],
        downloadingCount: this.data.downloadingCount - 1,
        completedCount: this.data.completedCount + 1
      })

      app.showToast('下载完成', 'success')
    }
  },

  // 获取文件扩展名
  getFileExtension(title) {
    if (title.includes('小说') || title.includes('文学')) return 'epub'
    if (title.includes('教程') || title.includes('资料')) return 'pdf'
    if (title.includes('图片') || title.includes('壁纸')) return 'zip'
    if (title.includes('视频')) return 'mp4'
    return 'file'
  },

  // 暂停下载
  pauseDownload(e) {
    const id = e.currentTarget.dataset.id
    const downloadingList = this.data.downloadingList.map(item => {
      if (item.id === id) {
        return {
          ...item,
          status: 'paused',
          speed: '0KB/s',
          remainingTime: '已暂停'
        }
      }
      return item
    })

    this.setData({
      downloadingList,
      downloadingCount: downloadingList.filter(item => item.status === 'downloading').length
    })
  },

  // 恢复下载
  resumeDownload(e) {
    const id = e.currentTarget.dataset.id
    const downloadingList = this.data.downloadingList.map(item => {
      if (item.id === id) {
        return {
          ...item,
          status: 'downloading',
          speed: '256KB/s',
          remainingTime: '计算中...'
        }
      }
      return item
    })

    this.setData({
      downloadingList,
      downloadingCount: downloadingList.filter(item => item.status === 'downloading').length
    })
  },

  // 取消下载
  cancelDownload(e) {
    const id = e.currentTarget.dataset.id
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个下载任务吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            downloadingList: this.data.downloadingList.filter(item => item.id !== id),
            totalDownloads: this.data.totalDownloads - 1,
            downloadingCount: this.data.downloadingCount - 1
          })
          app.showToast('已取消下载', 'success')
        }
      }
    })
  },

  // 全部暂停
  pauseAll() {
    const downloadingList = this.data.downloadingList.map(item => ({
      ...item,
      status: 'paused',
      speed: '0KB/s',
      remainingTime: '已暂停'
    }))

    this.setData({
      downloadingList,
      downloadingCount: 0
    })
  },

  // 全部开始
  resumeAll() {
    const downloadingList = this.data.downloadingList.map(item => ({
      ...item,
      status: 'downloading',
      speed: '256KB/s',
      remainingTime: '计算中...'
    }))

    this.setData({
      downloadingList,
      downloadingCount: downloadingList.length
    })
  },

  // 清除已完成
  clearCompleted() {
    if (this.data.completedList.length === 0) return

    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有已完成的下载记录吗？文件不会被删除。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            completedList: [],
            completedCount: 0,
            totalDownloads: this.data.downloadingList.length + this.data.failedList.length
          })
          app.showToast('已清除记录', 'success')
        }
      }
    })
  },

  // 切换已完成列表显示
  toggleCompleted() {
    this.setData({
      showCompleted: !this.data.showCompleted
    })
  },

  // 打开文件
  openFile(e) {
    const id = e.currentTarget.dataset.id
    const item = this.data.completedList.find(item => item.id === id)
    
    wx.showModal({
      title: '打开文件',
      content: `文件路径：${item.filePath}`,
      showCancel: false
    })
  },

  // 分享文件
  shareFile(e) {
    const id = e.currentTarget.dataset.id
    const item = this.data.completedList.find(item => item.id === id)
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 删除文件
  deleteFile(e) {
    const id = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个文件吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            completedList: this.data.completedList.filter(item => item.id !== id),
            completedCount: this.data.completedCount - 1,
            totalDownloads: this.data.totalDownloads - 1
          })
          app.showToast('文件已删除', 'success')
        }
      }
    })
  },

  // 重试下载
  retryDownload(e) {
    const id = e.currentTarget.dataset.id
    const failedItem = this.data.failedList.find(item => item.id === id)
    
    if (failedItem) {
      const newDownloadItem = {
        ...failedItem,
        totalSize: '2.5MB',
        downloadedSize: '0MB',
        progress: 0,
        speed: '256KB/s',
        remainingTime: '计算中...',
        status: 'downloading'
      }

      this.setData({
        failedList: this.data.failedList.filter(item => item.id !== id),
        downloadingList: [...this.data.downloadingList, newDownloadItem],
        downloadingCount: this.data.downloadingCount + 1
      })

      app.showToast('重新开始下载', 'success')
    }
  },

  // 移除失败项
  removeFailedItem(e) {
    const id = e.currentTarget.dataset.id
    this.setData({
      failedList: this.data.failedList.filter(item => item.id !== id),
      totalDownloads: this.data.totalDownloads - 1
    })
  },

  // 去首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
