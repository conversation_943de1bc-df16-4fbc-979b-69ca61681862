<!--pages/detail/detail.wxml-->
<view class="container">
  <!-- 资源头部信息 -->
  <view class="resource-header">
    <image class="resource-cover" src="{{resource.cover}}" mode="aspectFill"></image>
    <view class="resource-info">
      <text class="resource-title">{{resource.title}}</text>
      <text class="resource-desc">{{resource.description}}</text>
      <view class="resource-meta">
        <view class="meta-item">
          <image class="meta-icon" src="/images/category.png"></image>
          <text>{{resource.categoryName}}</text>
        </view>
        <view class="meta-item">
          <image class="meta-icon" src="/images/size.png"></image>
          <text>{{resource.size}}</text>
        </view>
        <view class="meta-item">
          <image class="meta-icon" src="/images/download.png"></image>
          <text>{{resource.downloads}}次下载</text>
        </view>
      </view>
      <view class="resource-tags">
        <text class="tag" wx:for="{{resource.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-bar">
    <button class="action-btn secondary" bindtap="toggleFavorite">
      <image src="/images/{{resource.isFavorite ? 'heart-fill' : 'heart'}}.png"></image>
      <text>{{resource.isFavorite ? '已收藏' : '收藏'}}</text>
    </button>
    <button class="action-btn secondary" bindtap="shareResource">
      <image src="/images/share.png"></image>
      <text>分享</text>
    </button>
    <button class="action-btn primary" bindtap="downloadResource">
      <image src="/images/download-white.png"></image>
      <text>立即下载</text>
    </button>
  </view>

  <!-- 详细信息 -->
  <view class="detail-section">
    <view class="section-title">详细信息</view>
    <view class="detail-list">
      <view class="detail-item">
        <text class="detail-label">文件名称</text>
        <text class="detail-value">{{resource.fileName}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">文件大小</text>
        <text class="detail-value">{{resource.size}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">文件格式</text>
        <text class="detail-value">{{resource.format}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">上传时间</text>
        <text class="detail-value">{{resource.createTime}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">下载次数</text>
        <text class="detail-value">{{resource.downloads}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">文件来源</text>
        <text class="detail-value">{{resource.source || '官方整理'}}</text>
      </view>
    </view>
  </view>

  <!-- 内容预览 -->
  <view class="preview-section" wx:if="{{resource.preview}}">
    <view class="section-title">内容预览</view>
    <view class="preview-content">
      <image wx:if="{{resource.type === 'image'}}" class="preview-image" src="{{resource.preview}}" mode="widthFix"></image>
      <text wx:elif="{{resource.type === 'text'}}" class="preview-text">{{resource.preview}}</text>
      <view wx:else class="preview-placeholder">
        <image src="/images/file.png"></image>
        <text>暂无预览</text>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="recommend-section">
    <view class="section-title">相关推荐</view>
    <scroll-view class="recommend-scroll" scroll-x="true" show-scrollbar="false">
      <view class="recommend-item" wx:for="{{relatedResources}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <image class="recommend-cover" src="{{item.cover}}" mode="aspectFill"></image>
        <view class="recommend-info">
          <text class="recommend-title">{{item.title}}</text>
          <view class="recommend-meta">
            <text class="recommend-size">{{item.size}}</text>
            <text class="recommend-downloads">{{item.downloads}}下载</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 评论区域 -->
  <view class="comment-section">
    <view class="section-title">
      <text>用户评价</text>
      <text class="comment-count">({{comments.length}})</text>
    </view>
    
    <!-- 评论输入 -->
    <view class="comment-input-area">
      <input class="comment-input" placeholder="写下你的评价..." value="{{commentText}}" bindinput="onCommentInput"></input>
      <button class="comment-submit" bindtap="submitComment" disabled="{{!commentText.trim()}}">发表</button>
    </view>

    <!-- 评论列表 -->
    <view class="comment-list">
      <view class="comment-item" wx:for="{{comments}}" wx:key="id">
        <image class="comment-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="comment-content">
          <view class="comment-header">
            <text class="comment-name">{{item.nickname}}</text>
            <text class="comment-time">{{item.createTime}}</text>
          </view>
          <text class="comment-text">{{item.content}}</text>
        </view>
      </view>
    </view>

    <!-- 空评论状态 -->
    <view class="empty-comments" wx:if="{{comments.length === 0}}">
      <image src="/images/comment.png"></image>
      <text>暂无评价，快来抢沙发吧~</text>
    </view>
  </view>
</view>

<!-- 下载进度弹窗 -->
<view class="download-modal" wx:if="{{showDownloadModal}}">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">下载进度</text>
      <image class="modal-close" src="/images/close.png" bindtap="hideDownloadModal"></image>
    </view>
    <view class="download-info">
      <text class="download-name">{{resource.fileName}}</text>
      <text class="download-progress-text">{{downloadProgress}}%</text>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{downloadProgress}}%"></view>
    </view>
    <view class="download-speed">
      <text>下载速度: {{downloadSpeed}}</text>
      <text>剩余时间: {{remainingTime}}</text>
    </view>
  </view>
</view>
