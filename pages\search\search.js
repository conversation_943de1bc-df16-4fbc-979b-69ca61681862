// pages/search/search.js
const app = getApp()

Page({
  data: {
    keyword: '',
    autoFocus: true,
    hasSearched: false,
    suggestions: [],
    hotKeywords: ['人工智能', '健康养生', '科技前沿', '教育学习', '生活百科', '旅游美食', '财经商业', '文化艺术'],
    searchHistory: [],
    searchResults: [],
    totalResults: 0,
    page: 1,
    hasMore: true,
    loading: false,
    showFilterModal: false,
    categories: [],
    filterCategory: '',
    filterSize: ''
  },

  onLoad() {
    this.setData({
      categories: app.globalData.categories
    })
    this.loadSearchHistory()
  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = wx.getStorageSync('searchHistory') || []
    this.setData({ searchHistory: history })
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    let history = wx.getStorageSync('searchHistory') || []
    
    // 移除重复项
    history = history.filter(item => item !== keyword)
    
    // 添加到开头
    history.unshift(keyword)
    
    // 限制历史记录数量
    if (history.length > 10) {
      history = history.slice(0, 10)
    }
    
    wx.setStorageSync('searchHistory', history)
    this.setData({ searchHistory: history })
  },

  // 输入事件
  onInput(e) {
    const keyword = e.detail.value
    this.setData({ keyword })
    
    if (keyword.trim()) {
      this.getSuggestions(keyword)
    } else {
      this.setData({ suggestions: [] })
    }
  },

  // 获取搜索建议
  getSuggestions(keyword) {
    // 模拟搜索建议
    const allSuggestions = [
      '人工智能应用', '人工智能发展', '人工智能未来',
      '健康养生知识', '健康养生方法', '健康养生食谱',
      '科技前沿资讯', '科技前沿趋势', '科技前沿发展',
      '教育学习方法', '教育学习技巧', '教育学习心得'
    ]

    const suggestions = allSuggestions.filter(item =>
      item.toLowerCase().includes(keyword.toLowerCase())
    ).slice(0, 5)

    this.setData({ suggestions })
  },

  // 搜索
  onSearch() {
    const keyword = this.data.keyword.trim()
    if (!keyword) return
    
    this.saveSearchHistory(keyword)
    this.performSearch(keyword)
  },

  // 执行搜索
  performSearch(keyword) {
    this.setData({
      hasSearched: true,
      searchResults: [],
      page: 1,
      hasMore: true,
      loading: true,
      autoFocus: false
    })
    
    this.loadSearchResults(keyword)
  },

  // 加载搜索结果
  loadSearchResults(keyword) {
    if (this.data.loading && this.data.page > 1) return
    
    this.setData({ loading: true })
    
    // 模拟API请求
    setTimeout(() => {
      const mockResults = this.generateMockResults(keyword)
      
      this.setData({
        searchResults: this.data.page === 1 ? mockResults : [...this.data.searchResults, ...mockResults],
        totalResults: 25, // 模拟总数
        page: this.data.page + 1,
        loading: false,
        hasMore: this.data.page < 3 // 模拟只有3页
      })
    }, 1000)
  },

  // 生成模拟搜索结果
  generateMockResults(keyword) {
    const results = [
      {
        id: Date.now() + 1,
        title: `人工智能在医疗领域的应用 - ${keyword}相关`,
        summary: 'AI技术正在革命性地改变医疗诊断和治疗方式，为患者带来更精准的医疗服务...',
        cover: '/images/article1.jpg',
        categoryName: '科技前沿',
        author: '医疗科技专家',
        views: 1234,
        publishTime: '2024-01-15',
        tags: ['AI', '医疗', '科技']
      },
      {
        id: Date.now() + 2,
        title: `健康养生新理念 - ${keyword}`,
        summary: '现代人的生活节奏越来越快，如何在忙碌中保持健康成为重要话题...',
        cover: '/images/article2.jpg',
        categoryName: '健康养生',
        author: '养生专家',
        views: 856,
        publishTime: '2024-01-14',
        tags: ['健康', '养生', '生活']
      },
      {
        id: Date.now() + 3,
        title: `教育改革的新思路 - ${keyword}`,
        summary: '教育是国家发展的基石，如何推进教育改革是当前的重要议题...',
        cover: '/images/article3.jpg',
        categoryName: '教育学习',
        author: '教育学者',
        views: 567,
        publishTime: '2024-01-13',
        tags: ['教育', '改革', '学习']
      }
    ]

    return results
  },

  // 选择搜索建议
  selectSuggestion(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ keyword })
    this.performSearch(keyword)
  },

  // 选择热门关键词
  selectHotKeyword(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ keyword })
    this.performSearch(keyword)
  },

  // 选择搜索历史
  selectHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ keyword })
    this.performSearch(keyword)
  },

  // 删除搜索历史项
  deleteHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    let history = this.data.searchHistory.filter(item => item !== keyword)
    
    wx.setStorageSync('searchHistory', history)
    this.setData({ searchHistory: history })
  },

  // 清除搜索历史
  clearHistory() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('searchHistory')
          this.setData({ searchHistory: [] })
        }
      }
    })
  },

  // 清除输入
  clearInput() {
    this.setData({
      keyword: '',
      suggestions: [],
      hasSearched: false,
      autoFocus: true
    })
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 显示筛选
  showFilter() {
    this.setData({ showFilterModal: true })
  },

  // 隐藏筛选
  hideFilter() {
    this.setData({ showFilterModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 选择筛选分类
  selectFilterCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ filterCategory: category })
  },

  // 选择筛选大小
  selectFilterSize(e) {
    const size = e.currentTarget.dataset.size
    this.setData({ filterSize: size })
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterCategory: '',
      filterSize: ''
    })
  },

  // 应用筛选
  applyFilter() {
    this.setData({ showFilterModal: false })
    
    // 重新搜索
    if (this.data.keyword) {
      this.performSearch(this.data.keyword)
    }
  },

  // 去文章详情页面
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/article/article?id=${id}`
    })
  },

  // 阅读文章
  readArticle(e) {
    const id = e.currentTarget.dataset.id

    // 直接跳转到文章页面
    wx.navigateTo({
      url: `/pages/article/article?id=${id}`
    })
  },

  // 加载更多
  loadMore() {
    if (this.data.keyword) {
      this.loadSearchResults(this.data.keyword)
    }
  },

  // 去分类页面
  goToCategory() {
    wx.switchTab({
      url: '/pages/category/category'
    })
  }
})
