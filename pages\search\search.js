// pages/search/search.js
const app = getApp()

Page({
  data: {
    keyword: '',
    autoFocus: true,
    hasSearched: false,
    suggestions: [],
    hotKeywords: ['三体', 'JavaScript', '壁纸', 'Python', '小说', '教程', '素材', '工具'],
    searchHistory: [],
    searchResults: [],
    totalResults: 0,
    page: 1,
    hasMore: true,
    loading: false,
    showFilterModal: false,
    categories: [],
    filterCategory: '',
    filterSize: ''
  },

  onLoad() {
    this.setData({
      categories: app.globalData.categories
    })
    this.loadSearchHistory()
  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = wx.getStorageSync('searchHistory') || []
    this.setData({ searchHistory: history })
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    let history = wx.getStorageSync('searchHistory') || []
    
    // 移除重复项
    history = history.filter(item => item !== keyword)
    
    // 添加到开头
    history.unshift(keyword)
    
    // 限制历史记录数量
    if (history.length > 10) {
      history = history.slice(0, 10)
    }
    
    wx.setStorageSync('searchHistory', history)
    this.setData({ searchHistory: history })
  },

  // 输入事件
  onInput(e) {
    const keyword = e.detail.value
    this.setData({ keyword })
    
    if (keyword.trim()) {
      this.getSuggestions(keyword)
    } else {
      this.setData({ suggestions: [] })
    }
  },

  // 获取搜索建议
  getSuggestions(keyword) {
    // 模拟搜索建议
    const allSuggestions = [
      '三体全集', '三体小说', '三体电影',
      'JavaScript教程', 'JavaScript高级', 'JavaScript入门',
      '高清壁纸', '4K壁纸', '手机壁纸',
      'Python入门', 'Python爬虫', 'Python教程'
    ]
    
    const suggestions = allSuggestions.filter(item => 
      item.toLowerCase().includes(keyword.toLowerCase())
    ).slice(0, 5)
    
    this.setData({ suggestions })
  },

  // 搜索
  onSearch() {
    const keyword = this.data.keyword.trim()
    if (!keyword) return
    
    this.saveSearchHistory(keyword)
    this.performSearch(keyword)
  },

  // 执行搜索
  performSearch(keyword) {
    this.setData({
      hasSearched: true,
      searchResults: [],
      page: 1,
      hasMore: true,
      loading: true,
      autoFocus: false
    })
    
    this.loadSearchResults(keyword)
  },

  // 加载搜索结果
  loadSearchResults(keyword) {
    if (this.data.loading && this.data.page > 1) return
    
    this.setData({ loading: true })
    
    // 模拟API请求
    setTimeout(() => {
      const mockResults = this.generateMockResults(keyword)
      
      this.setData({
        searchResults: this.data.page === 1 ? mockResults : [...this.data.searchResults, ...mockResults],
        totalResults: 25, // 模拟总数
        page: this.data.page + 1,
        loading: false,
        hasMore: this.data.page < 3 // 模拟只有3页
      })
    }, 1000)
  },

  // 生成模拟搜索结果
  generateMockResults(keyword) {
    const results = [
      {
        id: Date.now() + 1,
        title: `《三体》全集 - ${keyword}相关`,
        description: '刘慈欣科幻小说三部曲完整版',
        cover: '/images/book1.jpg',
        categoryName: '小说文学',
        size: '2.5MB',
        downloads: 1234,
        tags: ['科幻', '经典', '完结']
      },
      {
        id: Date.now() + 2,
        title: `JavaScript高级教程 - ${keyword}`,
        description: '前端开发必备学习资料',
        cover: '/images/study1.jpg',
        categoryName: '学习资料',
        size: '15.8MB',
        downloads: 856,
        tags: ['编程', 'JavaScript', 'PDF']
      },
      {
        id: Date.now() + 3,
        title: `精美壁纸合集 - ${keyword}`,
        description: '4K高清桌面壁纸素材',
        cover: '/images/image1.jpg',
        categoryName: '图片素材',
        size: '45.2MB',
        downloads: 567,
        tags: ['壁纸', '4K', '高清']
      }
    ]
    
    return results
  },

  // 选择搜索建议
  selectSuggestion(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ keyword })
    this.performSearch(keyword)
  },

  // 选择热门关键词
  selectHotKeyword(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ keyword })
    this.performSearch(keyword)
  },

  // 选择搜索历史
  selectHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ keyword })
    this.performSearch(keyword)
  },

  // 删除搜索历史项
  deleteHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    let history = this.data.searchHistory.filter(item => item !== keyword)
    
    wx.setStorageSync('searchHistory', history)
    this.setData({ searchHistory: history })
  },

  // 清除搜索历史
  clearHistory() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('searchHistory')
          this.setData({ searchHistory: [] })
        }
      }
    })
  },

  // 清除输入
  clearInput() {
    this.setData({
      keyword: '',
      suggestions: [],
      hasSearched: false,
      autoFocus: true
    })
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 显示筛选
  showFilter() {
    this.setData({ showFilterModal: true })
  },

  // 隐藏筛选
  hideFilter() {
    this.setData({ showFilterModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 选择筛选分类
  selectFilterCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ filterCategory: category })
  },

  // 选择筛选大小
  selectFilterSize(e) {
    const size = e.currentTarget.dataset.size
    this.setData({ filterSize: size })
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterCategory: '',
      filterSize: ''
    })
  },

  // 应用筛选
  applyFilter() {
    this.setData({ showFilterModal: false })
    
    // 重新搜索
    if (this.data.keyword) {
      this.performSearch(this.data.keyword)
    }
  },

  // 去详情页面
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 下载资源
  downloadResource(e) {
    const id = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认下载',
      content: '是否要下载这个资源？',
      success: (res) => {
        if (res.confirm) {
          app.showToast('开始下载...', 'success')
          
          // 跳转到下载页面
          wx.switchTab({
            url: '/pages/download/download'
          })
        }
      }
    })
  },

  // 加载更多
  loadMore() {
    if (this.data.keyword) {
      this.loadSearchResults(this.data.keyword)
    }
  },

  // 去分类页面
  goToCategory() {
    wx.switchTab({
      url: '/pages/category/category'
    })
  }
})
