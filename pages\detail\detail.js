// pages/detail/detail.js
const app = getApp()

Page({
  data: {
    resource: {},
    relatedResources: [],
    comments: [],
    commentText: '',
    showDownloadModal: false,
    downloadProgress: 0,
    downloadSpeed: '0KB/s',
    remainingTime: '--'
  },

  onLoad(options) {
    const id = options.id
    if (id) {
      this.loadResourceDetail(id)
      this.loadRelatedResources(id)
      this.loadComments(id)
    }
  },

  // 加载资源详情
  loadResourceDetail(id) {
    // 模拟API请求
    const mockResource = {
      id: parseInt(id),
      title: '《三体》全集',
      description: '刘慈欣科幻小说三部曲完整版，包含《三体》、《三体II：黑暗森林》、《三体III：死神永生》',
      cover: '/images/book1.jpg',
      categoryName: '小说文学',
      size: '2.5MB',
      downloads: 1234,
      fileName: '三体全集.epub',
      format: 'EPUB',
      createTime: '2024-01-15',
      source: '官方正版',
      type: 'text',
      preview: '文明的发展总是伴随着毁灭，这是宇宙的铁律。在三体世界中，人类面临着前所未有的挑战...',
      tags: ['科幻', '经典', '完结', '刘慈欣'],
      isFavorite: false,
      downloadUrl: 'https://example.com/download/santi.epub'
    }

    this.setData({ resource: mockResource })
  },

  // 加载相关推荐
  loadRelatedResources(id) {
    const relatedResources = [
      {
        id: 101,
        title: '《流浪地球》',
        cover: '/images/book2.jpg',
        size: '1.8MB',
        downloads: 856
      },
      {
        id: 102,
        title: '《球状闪电》',
        cover: '/images/book3.jpg',
        size: '2.1MB',
        downloads: 642
      },
      {
        id: 103,
        title: '《超新星纪元》',
        cover: '/images/book4.jpg',
        size: '3.2MB',
        downloads: 423
      }
    ]

    this.setData({ relatedResources })
  },

  // 加载评论
  loadComments(id) {
    const comments = [
      {
        id: 1,
        nickname: '科幻迷小王',
        avatar: '/images/avatar1.jpg',
        content: '非常经典的科幻小说，强烈推荐！',
        createTime: '2024-01-10'
      },
      {
        id: 2,
        nickname: '读书爱好者',
        avatar: '/images/avatar2.jpg',
        content: '三体系列真的很棒，每一部都很精彩',
        createTime: '2024-01-08'
      }
    ]

    this.setData({ comments })
  },

  // 切换收藏状态
  toggleFavorite() {
    const isFavorite = !this.data.resource.isFavorite
    this.setData({
      'resource.isFavorite': isFavorite
    })

    app.showToast(isFavorite ? '收藏成功' : '取消收藏', 'success')
  },

  // 分享资源
  shareResource() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 下载资源
  downloadResource() {
    wx.showModal({
      title: '确认下载',
      content: `确定要下载"${this.data.resource.title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.startDownload()
        }
      }
    })
  },

  // 开始下载
  startDownload() {
    this.setData({
      showDownloadModal: true,
      downloadProgress: 0
    })

    // 模拟下载进度
    const timer = setInterval(() => {
      let progress = this.data.downloadProgress + Math.random() * 10
      if (progress >= 100) {
        progress = 100
        clearInterval(timer)
        this.downloadComplete()
      }

      this.setData({
        downloadProgress: Math.floor(progress),
        downloadSpeed: `${Math.floor(Math.random() * 500 + 100)}KB/s`,
        remainingTime: progress < 100 ? `${Math.floor((100 - progress) / 10)}秒` : '完成'
      })
    }, 500)
  },

  // 下载完成
  downloadComplete() {
    app.showToast('下载完成', 'success')
    
    setTimeout(() => {
      this.setData({ showDownloadModal: false })
      
      // 跳转到下载页面
      wx.switchTab({
        url: '/pages/download/download'
      })
    }, 1000)
  },

  // 隐藏下载弹窗
  hideDownloadModal() {
    this.setData({ showDownloadModal: false })
  },

  // 评论输入
  onCommentInput(e) {
    this.setData({
      commentText: e.detail.value
    })
  },

  // 提交评论
  submitComment() {
    const content = this.data.commentText.trim()
    if (!content) return

    // 模拟提交评论
    const newComment = {
      id: Date.now(),
      nickname: '我',
      avatar: '/images/default-avatar.jpg',
      content: content,
      createTime: '刚刚'
    }

    this.setData({
      comments: [newComment, ...this.data.comments],
      commentText: ''
    })

    app.showToast('评论成功', 'success')
  },

  // 去详情页面（相关推荐）
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.redirectTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: this.data.resource.title,
      path: `/pages/detail/detail?id=${this.data.resource.id}`,
      imageUrl: this.data.resource.cover
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.data.resource.title,
      imageUrl: this.data.resource.cover
    }
  }
})
