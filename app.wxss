/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 通用按钮样式 */
.btn-primary {
  background-color: #1976D2;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

.btn-primary:active {
  background-color: #1565C0;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin: 20rpx;
  padding: 30rpx;
}

/* 列表项样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

/* 图标样式 */
.icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

/* 文本样式 */
.text-primary {
  color: #1976D2;
}

.text-secondary {
  color: #666;
  font-size: 24rpx;
}

.text-success {
  color: #4CAF50;
}

.text-warning {
  color: #FF9800;
}

.text-error {
  color: #F44336;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #1976D2;
  transition: width 0.3s ease;
}

/* 搜索框样式 */
.search-box {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  background-color: #e3f2fd;
  color: #1976D2;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin: 8rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

/* 加载更多样式 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
