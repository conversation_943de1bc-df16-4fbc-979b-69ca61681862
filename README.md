# 文章阅读助手 - 微信小程序

一个功能完整的微信小程序，支持文章浏览、阅读和管理。提供优质的阅读体验和个性化设置。

## 功能特性

### 🏠 首页
- 轮播图展示热门文章
- 分类导航快速访问
- 热门文章推荐
- 最新文章列表
- 下拉刷新和上拉加载

### 📂 分类浏览
- 8大主要分类：科技前沿、生活百科、健康养生、教育学习、娱乐八卦、财经商业、旅游美食、文化艺术
- 分类内文章列表展示
- 多种排序方式：时间、阅读量、热度
- 筛选功能

### 🔍 搜索功能
- 实时搜索建议
- 热门搜索关键词
- 搜索历史记录
- 高级筛选选项
- 搜索结果分页

### 📄 文章详情
- 详细文章内容展示
- 多主题阅读模式（日间/夜间/护眼）
- 字体大小和行间距调节
- 用户评论系统
- 收藏和分享功能
- 相关推荐文章

### 📖 阅读管理
- 正在阅读列表
- 阅读进度跟踪
- 阅读历史记录
- 阅读时长统计
- 收藏文章管理
- 个性化阅读设置

### 👤 个人中心
- 微信登录集成
- 阅读统计信息
- 收藏夹管理
- 浏览历史
- 阅读偏好设置

## 技术架构

### 前端技术
- **框架**: 微信小程序原生开发
- **样式**: WXSS + Flexbox布局
- **状态管理**: 页面级状态管理
- **组件化**: 可复用组件设计

### 核心模块
- **认证管理器** (`utils/auth.js`): 用户登录认证、权限管理
- **存储管理器** (`utils/storage.js`): 本地数据存储、缓存管理
- **API管理器** (`utils/api.js`): 网络请求封装、接口管理

### 数据存储
- **本地存储**: 用户设置、收藏、阅读历史
- **阅读设置**: 字体、主题、行间距等个性化配置
- **缓存机制**: 数据缓存和过期管理

## 项目结构

```
├── app.js                 # 应用入口文件
├── app.json              # 应用配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 搜索优化配置
├── pages/                # 页面目录
│   ├── index/           # 首页
│   ├── category/        # 分类页面
│   ├── article/         # 文章详情页面
│   ├── reading/         # 阅读管理页面
│   ├── profile/         # 个人中心
│   └── search/          # 搜索页面
├── utils/               # 工具类目录
│   ├── auth.js          # 认证管理器
│   ├── storage.js       # 存储管理器
│   └── api.js           # API管理器
└── images/              # 图片资源目录
```

## 快速开始

### 1. 环境准备
- 安装微信开发者工具
- 注册微信小程序账号
- 获取AppID

### 2. 项目配置
1. 在微信开发者工具中导入项目
2. 修改 `app.js` 中的 `baseUrl` 为你的后端API地址
3. 配置 `app.json` 中的相关权限

### 3. 后端接口
项目需要配套的后端API支持，主要接口包括：
- 用户认证接口
- 文章管理接口
- 分类管理接口
- 评论管理接口
- 搜索接口

### 4. 图片资源
需要准备以下图片资源放在 `images/` 目录：
- 导航图标 (home.png, category.png, reading.png, profile.png)
- 功能图标 (search.png, heart.png, share.png, read.png, etc.)
- 默认封面图片
- 轮播图片

## 主要功能实现

### 阅读功能
```javascript
// 使用认证管理器
const authManager = require('./utils/auth.js')

// 用户登录
authManager.forceLogin().then(userInfo => {
  console.log('登录成功:', userInfo.nickName)
})

// 记录阅读历史
const storageManager = require('./utils/storage.js')
storageManager.addHistory(articleId, articleData)
```

### 存储管理
```javascript
// 使用存储管理器
const storageManager = require('./utils/storage.js')

// 添加收藏
storageManager.addFavorite(articleId, articleData)

// 获取收藏列表
const favorites = storageManager.getFavorites()

// 添加阅读历史
storageManager.addHistory(articleId, articleData)
```

### API调用
```javascript
// 使用API管理器
const apiManager = require('./utils/api.js')

// 获取资源列表
apiManager.resource.getList({ page: 1, limit: 10 })
  .then(data => {
    console.log('资源列表:', data)
  })
  .catch(err => {
    console.error('请求失败:', err)
  })
```

## 部署说明

### 1. 小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核
4. 审核通过后发布上线

### 2. 服务器部署
- 部署后端API服务
- 配置HTTPS域名
- 在微信公众平台配置服务器域名

### 3. 注意事项
- 确保所有网络请求使用HTTPS
- 配置合法域名白名单
- 遵守微信小程序平台规范

## 开发规范

### 代码规范
- 使用ES6+语法
- 统一的命名规范
- 完善的错误处理
- 详细的注释说明

### 性能优化
- 图片懒加载
- 数据分页加载
- 合理的缓存策略
- 代码分包加载

### 用户体验
- 友好的加载状态
- 清晰的错误提示
- 流畅的页面切换
- 响应式设计

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发者。

---

**注意**: 这是一个演示项目，实际使用时需要：
1. 配置真实的后端API接口
2. 准备完整的图片资源
3. 申请微信小程序账号和相关权限
4. 遵守相关法律法规和平台规范
