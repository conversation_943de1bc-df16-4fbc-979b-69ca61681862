// pages/category/category.js
const app = getApp()

Page({
  data: {
    categories: [],
    currentCategory: null,
    resources: [],
    page: 1,
    hasMore: true,
    loading: false,
    showSort: false,
    sortType: 'time',
    sortText: '按时间排序'
  },

  onLoad(options) {
    this.setData({
      categories: app.globalData.categories
    })

    // 如果有分类ID参数，直接进入分类详情
    if (options.id) {
      this.selectCategoryById(parseInt(options.id))
    }
  },

  onShow() {
    // 如果在分类详情页，刷新数据
    if (this.data.currentCategory) {
      this.refreshResources()
    }
  },

  // 选择分类
  selectCategory(e) {
    const id = e.currentTarget.dataset.id
    this.selectCategoryById(id)
  },

  // 根据ID选择分类
  selectCategoryById(id) {
    const category = this.data.categories.find(cat => cat.id === id)
    if (category) {
      this.setData({
        currentCategory: category,
        resources: [],
        page: 1,
        hasMore: true
      })
      this.loadResources()
    }
  },

  // 刷新资源列表
  refreshResources() {
    this.setData({
      resources: [],
      page: 1,
      hasMore: true
    })
    this.loadResources()
  },

  // 加载资源列表
  loadResources() {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({ loading: true })

    // 模拟API请求
    setTimeout(() => {
      const mockResources = this.generateMockResources()
      
      this.setData({
        resources: [...this.data.resources, ...mockResources],
        page: this.data.page + 1,
        loading: false,
        hasMore: this.data.page < 3 // 模拟只有3页数据
      })
    }, 1000)
  },

  // 生成模拟数据
  generateMockResources() {
    const categoryId = this.data.currentCategory.id
    const page = this.data.page
    
    const mockData = {
      1: [ // 小说文学
        {
          id: page * 10 + 1,
          title: '《三体》全集',
          description: '刘慈欣科幻小说三部曲完整版',
          cover: '/images/book1.jpg',
          size: '2.5MB',
          downloads: 1234,
          createTime: '2024-01-15',
          tags: ['科幻', '经典', '完结']
        },
        {
          id: page * 10 + 2,
          title: '《流浪地球》',
          description: '刘慈欣短篇小说集',
          cover: '/images/book2.jpg',
          size: '1.8MB',
          downloads: 856,
          createTime: '2024-01-14',
          tags: ['科幻', '短篇']
        }
      ],
      2: [ // 学习资料
        {
          id: page * 10 + 1,
          title: 'JavaScript高级教程',
          description: '前端开发必备学习资料',
          cover: '/images/study1.jpg',
          size: '15.8MB',
          downloads: 2341,
          createTime: '2024-01-13',
          tags: ['编程', 'JavaScript', 'PDF']
        }
      ],
      3: [ // 图片素材
        {
          id: page * 10 + 1,
          title: '4K高清壁纸包',
          description: '精选4K分辨率桌面壁纸',
          cover: '/images/image1.jpg',
          size: '156MB',
          downloads: 567,
          createTime: '2024-01-12',
          tags: ['壁纸', '4K', '高清']
        }
      ]
    }

    return mockData[categoryId] || []
  },

  // 显示排序菜单
  showSortMenu() {
    this.setData({ showSort: true })
  },

  // 隐藏排序菜单
  hideSortMenu() {
    this.setData({ showSort: false })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 改变排序方式
  changeSort(e) {
    const type = e.currentTarget.dataset.type
    const sortTexts = {
      time: '按时间排序',
      downloads: '按下载量排序',
      size: '按大小排序'
    }

    this.setData({
      sortType: type,
      sortText: sortTexts[type],
      showSort: false
    })

    // 重新加载数据
    this.refreshResources()
  },

  // 显示筛选菜单
  showFilterMenu() {
    wx.showActionSheet({
      itemList: ['全部', '今天', '本周', '本月'],
      success: (res) => {
        console.log('选择了筛选条件:', res.tapIndex)
        // 这里可以根据选择的筛选条件重新加载数据
      }
    })
  },

  // 去详情页面
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 下载资源
  downloadResource(e) {
    const id = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认下载',
      content: '是否要下载这个资源？',
      success: (res) => {
        if (res.confirm) {
          this.startDownload(id)
        }
      }
    })
  },

  // 开始下载
  startDownload(id) {
    app.showToast('开始下载...', 'success')
    
    // 跳转到下载页面
    wx.switchTab({
      url: '/pages/download/download'
    })
  },

  // 加载更多
  loadMore() {
    this.loadResources()
  },

  // 返回分类列表
  onNavigationBarButtonTap() {
    if (this.data.currentCategory) {
      this.setData({
        currentCategory: null,
        resources: []
      })
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.currentCategory) {
      this.refreshResources()
    }
    
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})
