// pages/category/category.js
const app = getApp()

Page({
  data: {
    categories: [],
    currentCategory: null,
    resources: [],
    page: 1,
    hasMore: true,
    loading: false,
    showSort: false,
    sortType: 'time',
    sortText: '按时间排序'
  },

  onLoad(options) {
    this.setData({
      categories: app.globalData.categories
    })

    // 如果有分类ID参数，直接进入分类详情
    if (options.id) {
      this.selectCategoryById(parseInt(options.id))
    }
  },

  onShow() {
    // 如果在分类详情页，刷新数据
    if (this.data.currentCategory) {
      this.refreshResources()
    }
  },

  // 选择分类
  selectCategory(e) {
    const id = e.currentTarget.dataset.id
    this.selectCategoryById(id)
  },

  // 根据ID选择分类
  selectCategoryById(id) {
    const category = this.data.categories.find(cat => cat.id === id)
    if (category) {
      this.setData({
        currentCategory: category,
        resources: [],
        page: 1,
        hasMore: true
      })
      this.loadArticles()
    }
  },

  // 刷新资源列表
  refreshResources() {
    this.setData({
      resources: [],
      page: 1,
      hasMore: true
    })
    this.loadArticles()
  },

  // 加载文章列表
  loadArticles() {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({ loading: true })

    // 模拟API请求
    setTimeout(() => {
      const mockArticles = this.generateMockArticles()

      this.setData({
        resources: [...this.data.resources, ...mockArticles],
        page: this.data.page + 1,
        loading: false,
        hasMore: this.data.page < 3 // 模拟只有3页数据
      })
    }, 1000)
  },

  // 生成模拟数据
  generateMockArticles() {
    const categoryId = this.data.currentCategory.id
    const page = this.data.page

    const mockData = {
      1: [ // 科技前沿
        {
          id: page * 10 + 1,
          title: 'AI技术的最新突破',
          summary: '人工智能在各个领域都取得了重大进展，让我们来看看最新的技术突破...',
          cover: '/images/tech1.jpg',
          author: '科技观察者',
          views: 1234,
          publishTime: '2024-01-15',
          tags: ['AI', '科技', '突破']
        },
        {
          id: page * 10 + 2,
          title: '5G网络的未来发展',
          summary: '5G技术正在改变我们的生活方式，未来还会有哪些新的应用场景...',
          cover: '/images/tech2.jpg',
          author: '通信专家',
          views: 856,
          publishTime: '2024-01-14',
          tags: ['5G', '网络', '未来']
        }
      ],
      2: [ // 生活百科
        {
          id: page * 10 + 1,
          title: '居家收纳小技巧',
          summary: '让你的家变得更加整洁有序，这些收纳技巧一定要学会...',
          cover: '/images/life1.jpg',
          author: '生活达人',
          views: 2341,
          publishTime: '2024-01-13',
          tags: ['收纳', '居家', '技巧']
        }
      ],
      3: [ // 健康养生
        {
          id: page * 10 + 1,
          title: '春季养生指南',
          summary: '春天是养生的好时节，如何在春季保持身体健康...',
          cover: '/images/health1.jpg',
          author: '养生专家',
          views: 567,
          publishTime: '2024-01-12',
          tags: ['养生', '健康', '春季']
        }
      ]
    }

    return mockData[categoryId] || []
  },

  // 显示排序菜单
  showSortMenu() {
    this.setData({ showSort: true })
  },

  // 隐藏排序菜单
  hideSortMenu() {
    this.setData({ showSort: false })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 改变排序方式
  changeSort(e) {
    const type = e.currentTarget.dataset.type
    const sortTexts = {
      time: '按时间排序',
      downloads: '按下载量排序',
      size: '按大小排序'
    }

    this.setData({
      sortType: type,
      sortText: sortTexts[type],
      showSort: false
    })

    // 重新加载数据
    this.refreshResources()
  },

  // 显示筛选菜单
  showFilterMenu() {
    wx.showActionSheet({
      itemList: ['全部', '今天', '本周', '本月'],
      success: (res) => {
        console.log('选择了筛选条件:', res.tapIndex)
        // 这里可以根据选择的筛选条件重新加载数据
      }
    })
  },

  // 去文章详情页面
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/article/article?id=${id}`
    })
  },

  // 阅读文章
  readArticle(e) {
    const id = e.currentTarget.dataset.id

    // 直接跳转到文章页面
    wx.navigateTo({
      url: `/pages/article/article?id=${id}`
    })
  },

  // 加载更多
  loadMore() {
    this.loadArticles()
  },

  // 返回分类列表
  onNavigationBarButtonTap() {
    if (this.data.currentCategory) {
      this.setData({
        currentCategory: null,
        resources: []
      })
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.currentCategory) {
      this.refreshResources()
    }
    
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})
