// utils/storage.js - 本地存储工具类

class StorageManager {
  constructor() {
    this.prefix = 'resource_app_'
  }

  // 生成完整的key
  getKey(key) {
    return this.prefix + key
  }

  // 设置数据
  set(key, data) {
    try {
      const fullKey = this.getKey(key)
      wx.setStorageSync(fullKey, data)
      return true
    } catch (error) {
      console.error('Storage set error:', error)
      return false
    }
  }

  // 获取数据
  get(key, defaultValue = null) {
    try {
      const fullKey = this.getKey(key)
      const data = wx.getStorageSync(fullKey)
      return data !== '' ? data : defaultValue
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  }

  // 删除数据
  remove(key) {
    try {
      const fullKey = this.getKey(key)
      wx.removeStorageSync(fullKey)
      return true
    } catch (error) {
      console.error('Storage remove error:', error)
      return false
    }
  }

  // 清除所有应用数据
  clear() {
    try {
      const info = wx.getStorageInfoSync()
      const keys = info.keys.filter(key => key.startsWith(this.prefix))
      keys.forEach(key => {
        wx.removeStorageSync(key)
      })
      return true
    } catch (error) {
      console.error('Storage clear error:', error)
      return false
    }
  }

  // 获取存储信息
  getInfo() {
    try {
      const info = wx.getStorageInfoSync()
      const appKeys = info.keys.filter(key => key.startsWith(this.prefix))
      
      return {
        keys: appKeys,
        currentSize: info.currentSize,
        limitSize: info.limitSize,
        appKeyCount: appKeys.length
      }
    } catch (error) {
      console.error('Storage getInfo error:', error)
      return null
    }
  }

  // 收藏相关方法
  addFavorite(resourceId, resourceData) {
    const favorites = this.getFavorites()
    const exists = favorites.find(item => item.id === resourceId)
    
    if (!exists) {
      favorites.unshift({
        id: resourceId,
        ...resourceData,
        favoriteTime: new Date().toISOString()
      })
      this.set('favorites', favorites)
    }
    
    return !exists
  }

  removeFavorite(resourceId) {
    const favorites = this.getFavorites()
    const newFavorites = favorites.filter(item => item.id !== resourceId)
    this.set('favorites', newFavorites)
    return favorites.length !== newFavorites.length
  }

  getFavorites() {
    return this.get('favorites', [])
  }

  isFavorite(resourceId) {
    const favorites = this.getFavorites()
    return favorites.some(item => item.id === resourceId)
  }

  // 浏览历史相关方法
  addHistory(resourceId, resourceData) {
    const history = this.getHistory()
    
    // 移除已存在的记录
    const filteredHistory = history.filter(item => item.id !== resourceId)
    
    // 添加到开头
    filteredHistory.unshift({
      id: resourceId,
      ...resourceData,
      viewTime: new Date().toISOString()
    })
    
    // 限制历史记录数量
    if (filteredHistory.length > 100) {
      filteredHistory.splice(100)
    }
    
    this.set('history', filteredHistory)
  }

  getHistory() {
    return this.get('history', [])
  }

  clearHistory() {
    this.set('history', [])
  }

  removeHistoryItem(resourceId) {
    const history = this.getHistory()
    const newHistory = history.filter(item => item.id !== resourceId)
    this.set('history', newHistory)
  }

  // 搜索历史相关方法
  addSearchHistory(keyword) {
    const history = this.getSearchHistory()
    
    // 移除重复项
    const filteredHistory = history.filter(item => item !== keyword)
    
    // 添加到开头
    filteredHistory.unshift(keyword)
    
    // 限制数量
    if (filteredHistory.length > 20) {
      filteredHistory.splice(20)
    }
    
    this.set('searchHistory', filteredHistory)
  }

  getSearchHistory() {
    return this.get('searchHistory', [])
  }

  clearSearchHistory() {
    this.set('searchHistory', [])
  }

  removeSearchHistoryItem(keyword) {
    const history = this.getSearchHistory()
    const newHistory = history.filter(item => item !== keyword)
    this.set('searchHistory', newHistory)
  }

  // 下载记录相关方法
  addDownloadRecord(taskId, resourceData) {
    const records = this.getDownloadRecords()
    
    records.unshift({
      taskId,
      ...resourceData,
      downloadTime: new Date().toISOString()
    })
    
    // 限制记录数量
    if (records.length > 200) {
      records.splice(200)
    }
    
    this.set('downloadRecords', records)
  }

  getDownloadRecords() {
    return this.get('downloadRecords', [])
  }

  clearDownloadRecords() {
    this.set('downloadRecords', [])
  }

  // 用户设置相关方法
  setSetting(key, value) {
    const settings = this.getSettings()
    settings[key] = value
    this.set('settings', settings)
  }

  getSetting(key, defaultValue = null) {
    const settings = this.getSettings()
    return settings[key] !== undefined ? settings[key] : defaultValue
  }

  getSettings() {
    return this.get('settings', {
      autoDownload: false,
      downloadPath: 'downloads',
      maxConcurrentDownloads: 3,
      wifiOnlyDownload: false,
      notificationEnabled: true,
      theme: 'auto' // auto, light, dark
    })
  }

  // 缓存相关方法
  setCache(key, data, expireTime = null) {
    const cacheData = {
      data,
      timestamp: Date.now(),
      expireTime
    }
    this.set(`cache_${key}`, cacheData)
  }

  getCache(key) {
    const cacheData = this.get(`cache_${key}`)
    
    if (!cacheData) return null
    
    // 检查是否过期
    if (cacheData.expireTime && Date.now() > cacheData.expireTime) {
      this.remove(`cache_${key}`)
      return null
    }
    
    return cacheData.data
  }

  clearExpiredCache() {
    try {
      const info = wx.getStorageInfoSync()
      const cacheKeys = info.keys.filter(key => key.startsWith(this.prefix + 'cache_'))
      
      cacheKeys.forEach(key => {
        const cacheData = wx.getStorageSync(key)
        if (cacheData && cacheData.expireTime && Date.now() > cacheData.expireTime) {
          wx.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('Clear expired cache error:', error)
    }
  }

  // 获取存储使用情况统计
  getStorageStats() {
    const info = this.getInfo()
    if (!info) return null

    const favorites = this.getFavorites()
    const history = this.getHistory()
    const searchHistory = this.getSearchHistory()
    const downloadRecords = this.getDownloadRecords()

    return {
      totalKeys: info.appKeyCount,
      currentSize: info.currentSize,
      limitSize: info.limitSize,
      usagePercent: Math.round((info.currentSize / info.limitSize) * 100),
      favorites: favorites.length,
      history: history.length,
      searchHistory: searchHistory.length,
      downloadRecords: downloadRecords.length
    }
  }
}

// 创建全局存储管理器实例
const storageManager = new StorageManager()

module.exports = storageManager
