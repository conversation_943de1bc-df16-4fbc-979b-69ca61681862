// utils/download.js - 下载工具类
const app = getApp()

class DownloadManager {
  constructor() {
    this.downloadTasks = new Map() // 存储下载任务
    this.maxConcurrent = 3 // 最大并发下载数
    this.currentDownloads = 0 // 当前下载数
    this.downloadQueue = [] // 下载队列
  }

  // 开始下载
  startDownload(resource) {
    const taskId = this.generateTaskId()
    
    const task = {
      id: taskId,
      resourceId: resource.id,
      title: resource.title,
      cover: resource.cover,
      url: resource.downloadUrl,
      fileName: resource.fileName,
      totalSize: resource.size,
      downloadedSize: 0,
      progress: 0,
      status: 'waiting', // waiting, downloading, paused, completed, failed
      speed: '0KB/s',
      remainingTime: '--',
      createTime: new Date().toISOString(),
      error: null
    }

    this.downloadTasks.set(taskId, task)
    this.addToQueue(taskId)
    this.processQueue()
    
    return taskId
  }

  // 生成任务ID
  generateTaskId() {
    return 'download_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // 添加到队列
  addToQueue(taskId) {
    this.downloadQueue.push(taskId)
  }

  // 处理下载队列
  processQueue() {
    while (this.currentDownloads < this.maxConcurrent && this.downloadQueue.length > 0) {
      const taskId = this.downloadQueue.shift()
      this.executeDownload(taskId)
    }
  }

  // 执行下载
  executeDownload(taskId) {
    const task = this.downloadTasks.get(taskId)
    if (!task) return

    this.currentDownloads++
    task.status = 'downloading'
    task.startTime = Date.now()

    // 使用微信小程序下载API
    const downloadTask = wx.downloadFile({
      url: task.url,
      filePath: this.getFilePath(task.fileName),
      success: (res) => {
        if (res.statusCode === 200) {
          task.status = 'completed'
          task.progress = 100
          task.filePath = res.filePath
          task.completeTime = new Date().toLocaleString()
          this.onDownloadComplete(taskId)
        } else {
          task.status = 'failed'
          task.error = `下载失败，状态码: ${res.statusCode}`
          this.onDownloadFailed(taskId)
        }
      },
      fail: (err) => {
        task.status = 'failed'
        task.error = err.errMsg || '下载失败'
        this.onDownloadFailed(taskId)
      },
      complete: () => {
        this.currentDownloads--
        this.processQueue() // 处理队列中的下一个任务
      }
    })

    // 监听下载进度
    downloadTask.onProgressUpdate((res) => {
      task.progress = res.progress
      task.downloadedSize = this.formatSize(res.totalBytesWritten)
      task.totalSize = this.formatSize(res.totalBytesExpectedToWrite)
      task.speed = this.calculateSpeed(res.totalBytesWritten, task.startTime)
      task.remainingTime = this.calculateRemainingTime(res.progress, task.startTime)
      
      this.onProgressUpdate(taskId, task)
    })

    task.downloadTask = downloadTask
  }

  // 获取文件路径
  getFilePath(fileName) {
    return `${wx.env.USER_DATA_PATH}/downloads/${fileName}`
  }

  // 格式化文件大小
  formatSize(bytes) {
    if (bytes === 0) return '0B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i]
  }

  // 计算下载速度
  calculateSpeed(downloadedBytes, startTime) {
    const elapsed = (Date.now() - startTime) / 1000 // 秒
    if (elapsed === 0) return '0KB/s'
    
    const speed = downloadedBytes / elapsed
    return this.formatSize(speed) + '/s'
  }

  // 计算剩余时间
  calculateRemainingTime(progress, startTime) {
    if (progress === 0) return '--'
    
    const elapsed = (Date.now() - startTime) / 1000
    const remaining = (elapsed / progress) * (100 - progress)
    
    if (remaining < 60) {
      return Math.ceil(remaining) + '秒'
    } else if (remaining < 3600) {
      return Math.ceil(remaining / 60) + '分钟'
    } else {
      return Math.ceil(remaining / 3600) + '小时'
    }
  }

  // 暂停下载
  pauseDownload(taskId) {
    const task = this.downloadTasks.get(taskId)
    if (task && task.downloadTask) {
      task.downloadTask.abort()
      task.status = 'paused'
      this.currentDownloads--
      this.processQueue()
    }
  }

  // 恢复下载
  resumeDownload(taskId) {
    const task = this.downloadTasks.get(taskId)
    if (task && task.status === 'paused') {
      task.status = 'waiting'
      this.addToQueue(taskId)
      this.processQueue()
    }
  }

  // 取消下载
  cancelDownload(taskId) {
    const task = this.downloadTasks.get(taskId)
    if (task) {
      if (task.downloadTask) {
        task.downloadTask.abort()
        this.currentDownloads--
      }
      this.downloadTasks.delete(taskId)
      this.processQueue()
    }
  }

  // 获取任务信息
  getTask(taskId) {
    return this.downloadTasks.get(taskId)
  }

  // 获取所有任务
  getAllTasks() {
    return Array.from(this.downloadTasks.values())
  }

  // 获取指定状态的任务
  getTasksByStatus(status) {
    return this.getAllTasks().filter(task => task.status === status)
  }

  // 下载完成回调
  onDownloadComplete(taskId) {
    const task = this.downloadTasks.get(taskId)
    console.log('下载完成:', task.title)
    
    // 可以在这里添加完成后的处理逻辑
    // 比如通知用户、更新UI等
  }

  // 下载失败回调
  onDownloadFailed(taskId) {
    const task = this.downloadTasks.get(taskId)
    console.error('下载失败:', task.title, task.error)
  }

  // 进度更新回调
  onProgressUpdate(taskId, task) {
    // 可以在这里触发页面更新
    // 比如通过事件总线通知相关页面
  }

  // 清除已完成的任务
  clearCompletedTasks() {
    const completedTasks = this.getTasksByStatus('completed')
    completedTasks.forEach(task => {
      this.downloadTasks.delete(task.id)
    })
  }

  // 重试失败的下载
  retryDownload(taskId) {
    const task = this.downloadTasks.get(taskId)
    if (task && task.status === 'failed') {
      task.status = 'waiting'
      task.error = null
      task.progress = 0
      task.downloadedSize = 0
      this.addToQueue(taskId)
      this.processQueue()
    }
  }
}

// 创建全局下载管理器实例
const downloadManager = new DownloadManager()

module.exports = downloadManager
