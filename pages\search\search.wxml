<!--pages/search/search.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-header">
    <view class="search-box">
      <image class="search-icon" src="/images/search.png"></image>
      <input class="search-input" 
             placeholder="搜索资料..." 
             value="{{keyword}}" 
             bindinput="onInput" 
             bindconfirm="onSearch"
             focus="{{autoFocus}}"
             confirm-type="search"></input>
      <text class="search-cancel" bindtap="clearInput" wx:if="{{keyword}}">清除</text>
    </view>
    <text class="cancel-btn" bindtap="goBack">取消</text>
  </view>

  <!-- 搜索建议 -->
  <view class="search-suggestions" wx:if="{{keyword && !hasSearched}}">
    <view class="suggestion-item" 
          wx:for="{{suggestions}}" 
          wx:key="*this" 
          bindtap="selectSuggestion" 
          data-keyword="{{item}}">
      <image class="suggestion-icon" src="/images/search-gray.png"></image>
      <text class="suggestion-text">{{item}}</text>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view class="hot-search" wx:if="{{!keyword && !hasSearched}}">
    <view class="section-title">热门搜索</view>
    <view class="hot-tags">
      <text class="hot-tag" 
            wx:for="{{hotKeywords}}" 
            wx:key="*this" 
            bindtap="selectHotKeyword" 
            data-keyword="{{item}}">{{item}}</text>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{!keyword && !hasSearched && searchHistory.length > 0}}">
    <view class="section-header">
      <text class="section-title">搜索历史</text>
      <text class="clear-history" bindtap="clearHistory">清除</text>
    </view>
    <view class="history-list">
      <view class="history-item" 
            wx:for="{{searchHistory}}" 
            wx:key="*this" 
            bindtap="selectHistory" 
            data-keyword="{{item}}">
        <image class="history-icon" src="/images/history.png"></image>
        <text class="history-text">{{item}}</text>
        <image class="delete-icon" src="/images/close.png" bindtap="deleteHistory" data-keyword="{{item}}" catchtap="true"></image>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{hasSearched}}">
    <!-- 结果统计 -->
    <view class="result-stats">
      <text>找到 {{totalResults}} 个相关资源</text>
      <view class="filter-btn" bindtap="showFilter">
        <image src="/images/filter.png"></image>
        <text>筛选</text>
      </view>
    </view>

    <!-- 结果列表 -->
    <view class="result-list">
      <view class="result-item" 
            wx:for="{{searchResults}}" 
            wx:key="id" 
            bindtap="goToDetail" 
            data-id="{{item.id}}">
        <image class="result-cover" src="{{item.cover}}" mode="aspectFill"></image>
        <view class="result-content">
          <text class="result-title">{{item.title}}</text>
          <text class="result-desc">{{item.description}}</text>
          <view class="result-meta">
            <text class="result-category">{{item.categoryName}}</text>
            <text class="result-size">{{item.size}}</text>
            <text class="result-downloads">{{item.downloads}}下载</text>
          </view>
          <view class="result-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
        <button class="download-btn" bindtap="downloadResource" data-id="{{item.id}}" catchtap="true">
          <image src="/images/download.png"></image>
        </button>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
      <text wx:if="{{!loading}}">点击加载更多</text>
      <text wx:else>加载中...</text>
    </view>

    <!-- 无结果 -->
    <view class="no-results" wx:if="{{searchResults.length === 0 && !loading}}">
      <image class="no-results-icon" src="/images/no-results.png"></image>
      <text class="no-results-title">没有找到相关资源</text>
      <text class="no-results-desc">试试其他关键词或浏览分类</text>
      <button class="browse-btn" bindtap="goToCategory">
        <text>浏览分类</text>
      </button>
    </view>
  </view>

  <!-- 筛选弹窗 -->
  <view class="filter-modal" wx:if="{{showFilterModal}}" bindtap="hideFilter">
    <view class="filter-content" catchtap="stopPropagation">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <image class="filter-close" src="/images/close.png" bindtap="hideFilter"></image>
      </view>
      
      <view class="filter-section">
        <text class="filter-label">分类</text>
        <view class="filter-options">
          <text class="filter-option {{filterCategory === '' ? 'active' : ''}}" 
                bindtap="selectFilterCategory" 
                data-category="">全部</text>
          <text class="filter-option {{filterCategory === item.name ? 'active' : ''}}" 
                wx:for="{{categories}}" 
                wx:key="id" 
                bindtap="selectFilterCategory" 
                data-category="{{item.name}}">{{item.name}}</text>
        </view>
      </view>

      <view class="filter-section">
        <text class="filter-label">文件大小</text>
        <view class="filter-options">
          <text class="filter-option {{filterSize === '' ? 'active' : ''}}" 
                bindtap="selectFilterSize" 
                data-size="">全部</text>
          <text class="filter-option {{filterSize === 'small' ? 'active' : ''}}" 
                bindtap="selectFilterSize" 
                data-size="small">小于10MB</text>
          <text class="filter-option {{filterSize === 'medium' ? 'active' : ''}}" 
                bindtap="selectFilterSize" 
                data-size="medium">10MB-100MB</text>
          <text class="filter-option {{filterSize === 'large' ? 'active' : ''}}" 
                bindtap="selectFilterSize" 
                data-size="large">大于100MB</text>
        </view>
      </view>

      <view class="filter-actions">
        <button class="filter-reset" bindtap="resetFilter">重置</button>
        <button class="filter-confirm" bindtap="applyFilter">确定</button>
      </view>
    </view>
  </view>
</view>
