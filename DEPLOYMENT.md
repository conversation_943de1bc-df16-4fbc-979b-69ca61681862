# 部署指南

本文档详细说明如何部署"资料下载助手"微信小程序。

## 前期准备

### 1. 微信小程序账号
1. 访问 [微信公众平台](https://mp.weixin.qq.com/)
2. 注册小程序账号
3. 完成账号认证（个人或企业）
4. 获取 AppID

### 2. 开发工具
1. 下载 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. 安装并登录微信开发者工具

### 3. 服务器准备
- 准备HTTPS域名和SSL证书
- 部署后端API服务
- 配置数据库和文件存储

## 项目配置

### 1. 基础配置
1. 修改 `project.config.json` 中的 `appid` 为你的小程序AppID
2. 修改 `app.js` 中的 `baseUrl` 为你的API服务器地址
3. 检查 `app.json` 中的页面路径和权限配置

### 2. API配置
修改 `utils/api.js` 中的配置：
```javascript
constructor() {
  this.baseUrl = 'https://your-domain.com/api' // 替换为你的API地址
  this.timeout = 10000
}
```

### 3. 图片资源
1. 准备所需的图标和图片资源
2. 将图片文件放入 `images/` 目录
3. 参考 `images/README.md` 了解所需的图片文件

## 后端API开发

### 必需的API接口

#### 用户认证
```
POST /api/user/login          # 用户登录
GET  /api/user/info           # 获取用户信息
PUT  /api/user/info           # 更新用户信息
```

#### 资源管理
```
GET  /api/resources           # 获取资源列表
GET  /api/resources/:id       # 获取资源详情
GET  /api/resources/search    # 搜索资源
GET  /api/resources/hot       # 热门资源
GET  /api/resources/latest    # 最新资源
```

#### 分类管理
```
GET  /api/categories          # 获取分类列表
GET  /api/categories/:id/resources  # 获取分类下的资源
```

#### 下载管理
```
POST /api/download/url        # 获取下载链接
POST /api/download/record     # 记录下载
GET  /api/download/history    # 下载历史
```

#### 收藏功能
```
POST   /api/favorites         # 添加收藏
DELETE /api/favorites/:id     # 取消收藏
GET    /api/favorites         # 收藏列表
```

### API响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    // 具体数据
  }
}
```

## 微信公众平台配置

### 1. 服务器域名配置
在微信公众平台 -> 开发 -> 开发设置中配置：

**request合法域名**
```
https://your-api-domain.com
```

**downloadFile合法域名**
```
https://your-file-domain.com
```

**uploadFile合法域名**
```
https://your-upload-domain.com
```

### 2. 业务域名配置
如果需要在小程序中打开网页，需要配置业务域名。

### 3. 权限设置
确保小程序具有以下权限：
- 网络通信
- 数据缓存
- 文件系统
- 用户信息

## 本地开发

### 1. 导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录
4. 输入AppID
5. 点击"导入"

### 2. 开发调试
1. 在开发者工具中预览和调试
2. 使用真机调试测试功能
3. 检查网络请求和数据存储

### 3. 模拟器测试
- 测试各个页面功能
- 验证用户交互流程
- 检查异常情况处理

## 发布流程

### 1. 代码上传
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 确认上传

### 2. 提交审核
1. 登录微信公众平台
2. 进入版本管理
3. 选择开发版本提交审核
4. 填写审核信息

### 3. 发布上线
1. 审核通过后，点击"发布"
2. 小程序正式上线

## 云服务配置（可选）

### 微信云开发
如果使用微信云开发，可以简化后端开发：

1. 在微信公众平台开通云开发
2. 配置云函数、云数据库、云存储
3. 修改API调用为云开发API

### 第三方云服务
也可以使用其他云服务提供商：
- 腾讯云
- 阿里云
- 华为云
- AWS

## 性能优化

### 1. 代码优化
- 使用分包加载
- 图片懒加载
- 减少不必要的网络请求

### 2. 缓存策略
- 合理使用本地存储
- 设置数据缓存时间
- 清理过期缓存

### 3. 用户体验
- 添加加载状态
- 优化页面切换动画
- 处理网络异常情况

## 监控和维护

### 1. 数据统计
- 用户访问统计
- 功能使用统计
- 错误日志收集

### 2. 版本更新
- 定期更新功能
- 修复已知问题
- 优化用户体验

### 3. 用户反馈
- 收集用户反馈
- 及时响应问题
- 持续改进产品

## 注意事项

### 1. 合规要求
- 遵守微信小程序平台规范
- 确保内容合法合规
- 保护用户隐私

### 2. 安全考虑
- 使用HTTPS协议
- 验证用户输入
- 防止恶意攻击

### 3. 备份策略
- 定期备份代码
- 备份用户数据
- 制定灾难恢复计划

## 常见问题

### Q: 上传失败怎么办？
A: 检查网络连接，确认代码无语法错误，重试上传。

### Q: 审核被拒绝怎么办？
A: 根据审核意见修改代码，重新提交审核。

### Q: 如何处理用户反馈？
A: 建立反馈渠道，及时响应用户问题，持续优化产品。

### Q: 如何提高用户留存？
A: 优化用户体验，增加有价值的功能，建立用户激励机制。

---

如有其他问题，请参考微信小程序官方文档或联系技术支持。
