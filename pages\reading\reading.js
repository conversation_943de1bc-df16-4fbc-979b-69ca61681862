// pages/reading/reading.js
const app = getApp()
const storageManager = require('../../utils/storage.js')

Page({
  data: {
    currentReading: [],
    readingHistory: [],
    favoriteArticles: [],
    showHistory: true,
    showFavorites: true,
    totalReading: 0,
    todayReading: 0,
    readingTime: '0分钟',
    favoriteCount: 0,
    weeklyReading: 0,
    avgReadingTime: '0分钟',
    favoriteCategory: '暂无'
  },

  onLoad() {
    this.loadReadingData()
  },

  onShow() {
    this.loadReadingData()
  },

  // 加载阅读数据
  loadReadingData() {
    this.loadCurrentReading()
    this.loadReadingHistory()
    this.loadFavoriteArticles()
    this.calculateStats()
  },

  // 加载正在阅读的文章
  loadCurrentReading() {
    // 模拟正在阅读的文章数据
    const currentReading = [
      {
        id: 1,
        title: '人工智能在医疗领域的应用',
        cover: '/images/article1.jpg',
        progress: 65,
        readingTime: '25分钟',
        lastReadTime: '2小时前'
      },
      {
        id: 2,
        title: '如何培养孩子的阅读习惯',
        cover: '/images/article2.jpg',
        progress: 30,
        readingTime: '12分钟',
        lastReadTime: '1天前'
      }
    ]

    this.setData({ currentReading })
  },

  // 加载阅读历史
  loadReadingHistory() {
    const history = storageManager.getHistory()
    const readingHistory = history.map(item => ({
      ...item,
      readCount: Math.floor(Math.random() * 5) + 1,
      lastReadTime: this.formatTime(item.viewTime)
    }))

    this.setData({ readingHistory })
  },

  // 加载收藏文章
  loadFavoriteArticles() {
    const favorites = storageManager.getFavorites()
    const favoriteArticles = favorites.map(item => ({
      ...item,
      favoriteTime: this.formatTime(item.favoriteTime)
    }))

    this.setData({ 
      favoriteArticles,
      favoriteCount: favoriteArticles.length
    })
  },

  // 计算统计数据
  calculateStats() {
    const totalReading = this.data.readingHistory.length + this.data.currentReading.length
    const todayReading = Math.floor(Math.random() * 5) + 1
    const weeklyReading = Math.floor(Math.random() * 20) + 5
    
    // 模拟阅读时长计算
    const totalMinutes = totalReading * 15 + Math.floor(Math.random() * 100)
    const readingTime = this.formatDuration(totalMinutes)
    const avgReadingTime = this.formatDuration(Math.floor(totalMinutes / Math.max(totalReading, 1)))
    
    // 计算最喜欢的分类
    const categories = this.data.readingHistory.map(item => item.categoryName)
    const favoriteCategory = this.getMostFrequent(categories) || '暂无'

    this.setData({
      totalReading,
      todayReading,
      readingTime,
      weeklyReading,
      avgReadingTime,
      favoriteCategory
    })
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '未知时间'
    
    const now = new Date()
    const time = new Date(timestamp)
    const diff = now - time
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return time.toLocaleDateString()
    }
  },

  // 格式化时长
  formatDuration(minutes) {
    if (minutes < 60) {
      return `${minutes}分钟`
    } else {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return `${hours}小时${mins}分钟`
    }
  },

  // 获取最频繁的项目
  getMostFrequent(arr) {
    if (arr.length === 0) return null
    
    const frequency = {}
    let maxCount = 0
    let mostFrequent = null
    
    arr.forEach(item => {
      frequency[item] = (frequency[item] || 0) + 1
      if (frequency[item] > maxCount) {
        maxCount = frequency[item]
        mostFrequent = item
      }
    })
    
    return mostFrequent
  },

  // 继续阅读
  continueReading(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/article/article?id=${id}`
    })
  },

  // 从正在阅读中移除
  removeFromCurrent(e) {
    const id = e.currentTarget.dataset.id
    wx.showModal({
      title: '确认移除',
      content: '确定要从正在阅读中移除这篇文章吗？',
      success: (res) => {
        if (res.confirm) {
          const currentReading = this.data.currentReading.filter(item => item.id !== id)
          this.setData({ currentReading })
          app.showToast('已移除', 'success')
        }
      }
    })
  },

  // 重新阅读
  readAgain(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/article/article?id=${id}`
    })
  },

  // 从历史中移除
  removeFromHistory(e) {
    const id = e.currentTarget.dataset.id
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条阅读记录吗？',
      success: (res) => {
        if (res.confirm) {
          storageManager.removeHistoryItem(id)
          this.loadReadingHistory()
          this.calculateStats()
          app.showToast('已删除', 'success')
        }
      }
    })
  },

  // 阅读收藏的文章
  readFavorite(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/article/article?id=${id}`
    })
  },

  // 取消收藏
  removeFavorite(e) {
    const id = e.currentTarget.dataset.id
    wx.showModal({
      title: '确认取消收藏',
      content: '确定要取消收藏这篇文章吗？',
      success: (res) => {
        if (res.confirm) {
          storageManager.removeFavorite(id)
          this.loadFavoriteArticles()
          app.showToast('已取消收藏', 'success')
        }
      }
    })
  },

  // 切换历史记录显示
  toggleHistory() {
    this.setData({
      showHistory: !this.data.showHistory
    })
  },

  // 切换收藏显示
  toggleFavorites() {
    this.setData({
      showFavorites: !this.data.showFavorites
    })
  },

  // 清除历史记录
  clearHistory() {
    if (this.data.readingHistory.length === 0) {
      app.showToast('暂无历史记录', 'none')
      return
    }

    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有阅读历史记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          storageManager.clearHistory()
          this.setData({ readingHistory: [] })
          this.calculateStats()
          app.showToast('历史记录已清除', 'success')
        }
      }
    })
  },

  // 导出数据
  exportData() {
    wx.showModal({
      title: '导出数据',
      content: '数据导出功能正在开发中，敬请期待！',
      showCancel: false
    })
  },

  // 去首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
