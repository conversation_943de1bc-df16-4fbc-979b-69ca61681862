<!--pages/download/download.wxml-->
<view class="container">
  <!-- 顶部统计 -->
  <view class="stats-section">
    <view class="stats-item">
      <text class="stats-number">{{totalDownloads}}</text>
      <text class="stats-label">总下载</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{downloadingCount}}</text>
      <text class="stats-label">下载中</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{completedCount}}</text>
      <text class="stats-label">已完成</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{totalSize}}</text>
      <text class="stats-label">总大小</text>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-section">
    <view class="action-left">
      <button class="action-btn" bindtap="pauseAll" wx:if="{{downloadingCount > 0}}">
        <image src="/images/pause.png"></image>
        <text>全部暂停</text>
      </button>
      <button class="action-btn" bindtap="resumeAll" wx:else>
        <image src="/images/play.png"></image>
        <text>全部开始</text>
      </button>
    </view>
    <view class="action-right">
      <button class="action-btn" bindtap="clearCompleted">
        <image src="/images/clear.png"></image>
        <text>清除已完成</text>
      </button>
    </view>
  </view>

  <!-- 下载列表 -->
  <view class="download-list">
    <!-- 下载中 -->
    <view class="section-header" wx:if="{{downloadingList.length > 0}}">
      <text class="section-title">下载中 ({{downloadingList.length}})</text>
    </view>
    <view class="download-item downloading" wx:for="{{downloadingList}}" wx:key="id">
      <image class="item-cover" src="{{item.cover}}" mode="aspectFill"></image>
      <view class="item-content">
        <text class="item-title">{{item.title}}</text>
        <text class="item-info">{{item.downloadedSize}} / {{item.totalSize}}</text>
        <view class="progress-container">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%"></view>
          </view>
          <text class="progress-text">{{item.progress}}%</text>
        </view>
        <view class="item-meta">
          <text class="download-speed">{{item.speed}}</text>
          <text class="remaining-time">剩余 {{item.remainingTime}}</text>
        </view>
      </view>
      <view class="item-actions">
        <button class="action-icon-btn" bindtap="pauseDownload" data-id="{{item.id}}" wx:if="{{item.status === 'downloading'}}">
          <image src="/images/pause.png"></image>
        </button>
        <button class="action-icon-btn" bindtap="resumeDownload" data-id="{{item.id}}" wx:else>
          <image src="/images/play.png"></image>
        </button>
        <button class="action-icon-btn" bindtap="cancelDownload" data-id="{{item.id}}">
          <image src="/images/close.png"></image>
        </button>
      </view>
    </view>

    <!-- 已完成 -->
    <view class="section-header" wx:if="{{completedList.length > 0}}">
      <text class="section-title">已完成 ({{completedList.length}})</text>
      <text class="section-action" bindtap="toggleCompleted">{{showCompleted ? '收起' : '展开'}}</text>
    </view>
    <view class="download-item completed" wx:for="{{completedList}}" wx:key="id" wx:if="{{showCompleted}}">
      <image class="item-cover" src="{{item.cover}}" mode="aspectFill"></image>
      <view class="item-content">
        <text class="item-title">{{item.title}}</text>
        <text class="item-info">{{item.totalSize}} · {{item.completeTime}}</text>
        <view class="item-meta">
          <text class="file-path">{{item.filePath}}</text>
        </view>
      </view>
      <view class="item-actions">
        <button class="action-icon-btn" bindtap="openFile" data-id="{{item.id}}">
          <image src="/images/open.png"></image>
        </button>
        <button class="action-icon-btn" bindtap="shareFile" data-id="{{item.id}}">
          <image src="/images/share.png"></image>
        </button>
        <button class="action-icon-btn" bindtap="deleteFile" data-id="{{item.id}}">
          <image src="/images/delete.png"></image>
        </button>
      </view>
    </view>

    <!-- 失败的下载 -->
    <view class="section-header" wx:if="{{failedList.length > 0}}">
      <text class="section-title">下载失败 ({{failedList.length}})</text>
    </view>
    <view class="download-item failed" wx:for="{{failedList}}" wx:key="id">
      <image class="item-cover" src="{{item.cover}}" mode="aspectFill"></image>
      <view class="item-content">
        <text class="item-title">{{item.title}}</text>
        <text class="item-error">{{item.errorMessage}}</text>
      </view>
      <view class="item-actions">
        <button class="action-icon-btn" bindtap="retryDownload" data-id="{{item.id}}">
          <image src="/images/retry.png"></image>
        </button>
        <button class="action-icon-btn" bindtap="removeFailedItem" data-id="{{item.id}}">
          <image src="/images/close.png"></image>
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{totalDownloads === 0}}">
    <image class="empty-icon" src="/images/download-empty.png"></image>
    <text class="empty-title">暂无下载任务</text>
    <text class="empty-desc">去首页找些喜欢的资源下载吧</text>
    <button class="empty-btn" bindtap="goToHome">
      <text>去首页看看</text>
    </button>
  </view>

  <!-- 存储空间提示 -->
  <view class="storage-info" wx:if="{{totalDownloads > 0}}">
    <view class="storage-item">
      <text class="storage-label">已用空间</text>
      <text class="storage-value">{{usedStorage}} / {{totalStorage}}</text>
    </view>
    <view class="storage-bar">
      <view class="storage-fill" style="width: {{storagePercent}}%"></view>
    </view>
  </view>
</view>
