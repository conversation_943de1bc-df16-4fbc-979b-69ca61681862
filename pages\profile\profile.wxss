/* pages/profile/profile.wxss */

/* 用户信息区域 */
.user-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.user-desc {
  font-size: 26rpx;
  color: #666;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 用户统计 */
.user-stats {
  display: flex;
  padding: 30rpx 0;
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #1976D2;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  margin-bottom: 20rpx;
}

.menu-group {
  background-color: white;
  margin-bottom: 20rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 35rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 25rpx;
}

.menu-title {
  font-size: 30rpx;
  color: #333;
}

.menu-right {
  display: flex;
  align-items: center;
}

.menu-badge {
  background-color: #FF4444;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 15rpx;
  min-width: 32rpx;
  text-align: center;
}

.menu-version {
  font-size: 24rpx;
  color: #999;
  margin-right: 15rpx;
}

/* VIP会员区域 */
.vip-section {
  margin-bottom: 20rpx;
}

.vip-card {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 16rpx;
  padding: 40rpx;
  margin: 0 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vip-content {
  flex: 1;
}

.vip-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.vip-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.vip-features {
  display: flex;
  flex-direction: column;
}

.vip-feature {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.vip-btn {
  background-color: #333;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
  border: none;
}

.vip-btn::after {
  border: none;
}

/* 阅读统计区域 */
.reading-section {
  background-color: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.reading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.reading-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.reading-detail {
  font-size: 26rpx;
  color: #2E7D32;
}

.reading-info {
  display: flex;
  flex-direction: column;
}

.reading-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.reading-item:last-child {
  border-bottom: none;
}

.reading-label {
  font-size: 28rpx;
  color: #333;
}

.reading-value {
  font-size: 26rpx;
  color: #2E7D32;
  font-weight: bold;
}

/* 退出登录 */
.logout-section {
  padding: 0 20rpx;
  margin-bottom: 40rpx;
}

.logout-btn {
  width: 100%;
  background-color: #F44336;
  color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  font-size: 30rpx;
  border: none;
}

.logout-btn::after {
  border: none;
}
