// pages/index/index.js (内置诊断日志的最终完整代码)
const GameManager = require('../../utils/gameManager.js');

Page({
  data: {
    player: {
      name: "无名小卒", levelName: "炼气一层", level: 1,
      exp: 0, exp_needed: 100,
      health: 100, max_health: 100,
      mana: 50, max_mana: 50,
      attack: 10, defense: 5,
      inventory: [],
    },
    monster: null,
    levelNameMap: ["炼气一层", "炼气二层", "炼气三层", "炼气四层", "炼气五层", "炼气六层", "炼气七层", "炼气八层", "炼气九层", "炼气圆满", "筑基初期", "筑基中期", "筑基后期"],
    log: [],
    menu: [],
  },

  onLoad: function (options) {
    console.log("--- Fired: onLoad ---"); // 诊断日志
    const playerSave = wx.getStorageSync('playerSave');
    if (playerSave) {
      console.log("找到存档:", playerSave);
      // 兼容旧存档，为旧存档补上所有可能缺失的属性
      const player = this.ensurePlayerData(playerSave);
      this.setData({ player: player });
    } else {
      this.addLog("欢迎来到修仙世界！", "#ccc");
    }
    this.updateMenu();
  },
  
  // 统一的菜单点击事件
  onMenuTap: function(e) {
    const buttonId = e.currentTarget.dataset.id;
    console.log("---> Fired: onMenuTap, buttonId:", buttonId); // 诊断日志
    
    switch(buttonId) {
      case 'cultivate': this.cultivate(); break;
      case 'explore': this.explore(); break;
      case 'attack': this.attackMonster(); break;
      case 'escape': this.escape(); break;
      case 'inventory': this.openInventory(); break;
      default: console.log("未知按钮:", buttonId);
    }
  },

  updateMenu: function() {
    console.log("--- Fired: updateMenu ---");
    let menuButtons = [];
    if (this.data.monster && this.data.monster.health > 0) {
      menuButtons = [
        { id: 'attack', name: '攻击', color: '#c40000' },
        { id: 'escape', name: '逃跑', color: '#008080' },
        { id: 'skill', name: '法术', color: '#4682b4', disabled: true },
        { id: 'item', name: '道具', color: '#a0522d', disabled: true },
      ];
    } else {
      menuButtons = [
        { id: 'cultivate', name: '修炼', color: '#4b0082' },
        { id: 'explore', name: '探索', color: '#006400' },
        { id: 'inventory', name: '储物', color: '#a0522d', disabled: true },
        { id: 'player', name: '角色', color: '#4682b4', disabled: true },
      ];
    }
    this.setData({ menu: menuButtons });
  },

  cultivate: function() {
    console.log("--- Fired: cultivate ---");
    let player = this.data.player; 
    player.exp += 10;
    this.addLog('你修炼了一会儿，灵力增加了10点。', '#ccc');
    this.checkAndUpdateLevelUp(); 
    this.saveGame();
  },

  // ... attackMonster, escape, explore 等其他函数保持不变 ...
  attackMonster: function() { let player = this.data.player; let monster = this.data.monster; if (!monster || monster.health <= 0) return; let playerDamage = Math.max(1, player.attack - monster.defense); monster.health -= playerDamage; this.addLog(`你对${monster.name}造成了 ${playerDamage} 点伤害。`, "#00ff7f"); if (monster.health <= 0) { monster.health = 0; this.addLog(`${monster.name}被你击败了！`, "#ffd700"); player.exp += monster.exp_reward; this.addLog(`你获得了 ${monster.exp_reward} 点灵力。`, "#ffd700"); this.checkAndUpdateLevelUp(); this.setData({ monster: null }); this.updateMenu(); this.saveGame(); return; } let monsterDamage = Math.max(1, monster.attack - player.defense); player.health -= monsterDamage; this.addLog(`${monster.name}对你造成了 ${monsterDamage} 点伤害。`, "#ff6347"); if (player.health <= 0) { player.health = 0; this.setData({ player: player }); wx.showModal({ title: '你已阵亡', content: '是否消耗一次机会复活？', success: (res) => { if (res.confirm) { player.health = player.max_health; this.setData({ player: player }); this.saveGame(); this.addLog('你使用了复活机会，重获新生！', '#ff4500'); } else { this.addLog('你放弃了复活，轮回去了...', '#aaa'); } } }); return; } this.setData({ player: player, monster: monster }); this.saveGame(); },
  escape: function() { this.addLog("你决定三十六计走为上策！", "#00ced1"); this.setData({ monster: null }); this.updateMenu(); this.addLog("你成功逃脱了！", "#00ced1"); },
  explore: function() { this.addLog("你决定外出探索一番...", "#32cd32"); const newMonster = GameManager.spawnMonster(this.data.monster); if (newMonster) { this.setData({ monster: newMonster }); this.updateMenu(); } else { this.addLog("你四处逛了逛，但什么都没发现。", "#ccc"); } },
  openInventory: function() { console.log("当前背包内容:", this.data.player.inventory); this.addLog("你打开了背包，详情请在电脑调试器中查看。", "#00bfff"); },
  checkAndUpdateLevelUp: function() { let player = this.data.player; const levelUpMessage = GameManager.checkLevelUp(player, this.data.levelNameMap); if (levelUpMessage) { this.addLog(levelUpMessage, "#ff4500"); } this.setData({ player: player }); },
  saveGame: function() { try { wx.setStorageSync('playerSave', this.data.player); } catch (e) { console.error("存档失败", e); } },
  addLog: function(text, color = '#f0f0f0') { const newLog = { text: text, color: color }; const log = this.data.log.slice(0, 99); log.unshift(newLog); this.setData({ log: log }); },

  // --- 新增：确保玩家数据完整的辅助函数 ---
  ensurePlayerData: function(playerData) {
    const defaults = {
      name: "无名小卒", levelName: "炼气一层", level: 1,
      exp: 0, exp_needed: 100, health: 100, max_health: 100,
      mana: 50, max_mana: 50, attack: 10, defense: 5, inventory: [],
    };
    // 遍历所有默认属性，如果存档中没有，就给它补上
    for (let key in defaults) {
      if (playerData[key] === undefined) {
        playerData[key] = defaults[key];
      }
    }
    return playerData;
  }
})