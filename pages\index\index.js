// pages/index/index.js
const app = getApp()

Page({
  data: {
    banners: [
      {
        id: 1,
        image: '/images/banner1.jpg',
        url: '/pages/category/category?id=1'
      },
      {
        id: 2,
        image: '/images/banner2.jpg',
        url: '/pages/category/category?id=2'
      },
      {
        id: 3,
        image: '/images/banner3.jpg',
        url: '/pages/category/category?id=3'
      }
    ],
    categories: [],
    hotResources: [],
    latestResources: [],
    page: 1,
    hasMore: true,
    loading: false
  },

  onLoad() {
    this.initData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.setData({
      page: 1,
      latestResources: [],
      hasMore: true
    })
    this.loadLatestResources()
  },

  // 初始化数据
  initData() {
    this.setData({
      categories: app.globalData.categories.slice(0, 8) // 只显示前8个分类
    })
    this.loadHotResources()
    this.loadLatestResources()
  },

  // 加载热门资源
  loadHotResources() {
    // 模拟数据，实际应该从服务器获取
    const hotResources = [
      {
        id: 1,
        title: '《三体》全集',
        cover: '/images/book1.jpg',
        size: '2.5MB',
        downloads: 1234
      },
      {
        id: 2,
        title: 'JavaScript高级教程',
        cover: '/images/book2.jpg',
        size: '15.8MB',
        downloads: 856
      },
      {
        id: 3,
        title: '精美壁纸合集',
        cover: '/images/image1.jpg',
        size: '45.2MB',
        downloads: 2341
      },
      {
        id: 4,
        title: 'Python入门视频',
        cover: '/images/video1.jpg',
        size: '128MB',
        downloads: 567
      }
    ]
    this.setData({ hotResources })
  },

  // 加载最新资源
  loadLatestResources() {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({ loading: true })

    // 模拟API请求
    setTimeout(() => {
      const newResources = [
        {
          id: this.data.page * 10 + 1,
          title: '《流浪地球》小说',
          description: '刘慈欣科幻小说代表作，电影原著',
          cover: '/images/book3.jpg',
          categoryName: '小说文学',
          createTime: '2024-01-15',
          size: '1.2MB'
        },
        {
          id: this.data.page * 10 + 2,
          title: 'Vue.js开发指南',
          description: '前端框架Vue.js完整学习资料',
          cover: '/images/book4.jpg',
          categoryName: '学习资料',
          createTime: '2024-01-14',
          size: '8.5MB'
        },
        {
          id: this.data.page * 10 + 3,
          title: '高清风景图片包',
          description: '4K高清自然风景图片素材',
          cover: '/images/image2.jpg',
          categoryName: '图片素材',
          createTime: '2024-01-13',
          size: '156MB'
        }
      ]

      this.setData({
        latestResources: [...this.data.latestResources, ...newResources],
        page: this.data.page + 1,
        loading: false,
        hasMore: this.data.page < 5 // 模拟只有5页数据
      })
    }, 1000)
  },

  // 搜索
  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 轮播图点击
  onBannerTap(e) {
    const url = e.currentTarget.dataset.url
    if (url) {
      wx.navigateTo({ url })
    }
  },

  // 去分类页面
  goToCategory() {
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  // 去分类详情
  goToCategoryDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/category/category?id=${id}`
    })
  },

  // 去推荐页面
  goToRecommend() {
    wx.navigateTo({
      url: '/pages/category/category?type=hot'
    })
  },

  // 去详情页面
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 下载资源
  downloadResource(e) {
    const id = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认下载',
      content: '是否要下载这个资源？',
      success: (res) => {
        if (res.confirm) {
          this.startDownload(id)
        }
      }
    })
  },

  // 开始下载
  startDownload(id) {
    // 这里应该调用实际的下载逻辑
    app.showToast('开始下载...', 'success')
    
    // 跳转到下载页面
    wx.switchTab({
      url: '/pages/download/download'
    })
  },

  // 加载更多
  loadMore() {
    this.loadLatestResources()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      page: 1,
      latestResources: [],
      hasMore: true
    })
    this.loadLatestResources()
    this.loadHotResources()
    
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})
