/* pages/reading/reading.wxss */

/* 统计区域 */
.stats-section {
  display: flex;
  background-color: white;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 操作区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 30rpx 40rpx;
  margin-bottom: 20rpx;
}

.action-left,
.action-right {
  display: flex;
}

.action-btn {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 8rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  margin-right: 20rpx;
  border: none;
}

.action-btn:last-child {
  margin-right: 0;
}

.action-btn image {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.action-btn::after {
  border: none;
}

/* 阅读列表 */
.reading-list {
  background-color: white;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.section-action {
  font-size: 26rpx;
  color: #2E7D32;
}

.reading-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.reading-item:last-child {
  border-bottom: none;
}

.item-cover {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 30rpx;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-info {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.progress-fill {
  height: 100%;
  background-color: #2E7D32;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #2E7D32;
  font-weight: bold;
  min-width: 60rpx;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
}

.item-actions {
  display: flex;
  align-items: center;
}

.action-icon-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 15rpx;
  padding: 0;
  border: none;
}

.action-icon-btn image {
  width: 30rpx;
  height: 30rpx;
}

.action-icon-btn::after {
  border: none;
}

/* 阅读状态样式 */
.reading-item.current {
  background-color: #f8fff8;
}

.reading-item.history {
  background-color: #fafafa;
}

.reading-item.favorite {
  background-color: #fff8f8;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background-color: white;
  margin-bottom: 20rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.empty-btn {
  background-color: #2E7D32;
  color: white;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
  border: none;
}

.empty-btn::after {
  border: none;
}

/* 阅读统计 */
.reading-stats {
  background-color: white;
  padding: 30rpx 40rpx;
  margin-bottom: 20rpx;
}

.reading-stats .stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.reading-stats .stats-item:last-child {
  border-bottom: none;
}

.reading-stats .stats-label {
  font-size: 28rpx;
  color: #333;
}

.reading-stats .stats-value {
  font-size: 26rpx;
  color: #2E7D32;
  font-weight: bold;
}
