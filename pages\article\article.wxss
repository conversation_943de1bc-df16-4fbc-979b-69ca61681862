/* pages/article/article.wxss */

/* 主题样式 */
.container {
  min-height: 100vh;
  transition: all 0.3s ease;
}

.container.light {
  background-color: #ffffff;
  color: #333333;
}

.container.dark {
  background-color: #1a1a1a;
  color: #e0e0e0;
}

.container.sepia {
  background-color: #f7f3e9;
  color: #5c4b37;
}

/* 文章头部 */
.article-header {
  padding: 40rpx;
  border-bottom: 1rpx solid rgba(0,0,0,0.1);
}

.container.dark .article-header {
  border-bottom-color: rgba(255,255,255,0.1);
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 30rpx;
  display: block;
}

.article-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.article-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  background-color: #e8f5e8;
  color: #2E7D32;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.container.dark .tag {
  background-color: #2E7D32;
  color: #ffffff;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid rgba(0,0,0,0.1);
  background-color: rgba(255,255,255,0.9);
  backdrop-filter: blur(10rpx);
  position: sticky;
  top: 0;
  z-index: 100;
}

.container.dark .toolbar {
  background-color: rgba(26,26,26,0.9);
  border-bottom-color: rgba(255,255,255,0.1);
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 22rpx;
}

.tool-item image {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

/* 文章内容 */
.article-content {
  padding: 40rpx;
  line-height: 1.8;
  font-size: 32rpx;
}

.article-content h1,
.article-content h2,
.article-content h3 {
  font-weight: bold;
  margin: 40rpx 0 20rpx;
  line-height: 1.3;
}

.article-content h1 {
  font-size: 40rpx;
}

.article-content h2 {
  font-size: 36rpx;
}

.article-content h3 {
  font-size: 32rpx;
}

.article-content p {
  margin-bottom: 30rpx;
  text-indent: 2em;
}

.article-content img {
  width: 100%;
  border-radius: 12rpx;
  margin: 20rpx 0;
}

/* 相关推荐 */
.related-section {
  padding: 40rpx;
  border-top: 1rpx solid rgba(0,0,0,0.1);
}

.container.dark .related-section {
  border-top-color: rgba(255,255,255,0.1);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.related-list {
  display: flex;
  flex-direction: column;
}

.related-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
}

.related-item:last-child {
  border-bottom: none;
}

.related-cover {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.related-content {
  flex: 1;
}

.related-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related-meta {
  display: flex;
  align-items: center;
}

.related-author,
.related-views {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

/* 评论区域 */
.comment-section {
  padding: 40rpx;
  border-top: 1rpx solid rgba(0,0,0,0.1);
}

.container.dark .comment-section {
  border-top-color: rgba(255,255,255,0.1);
}

.comment-input-area {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.comment-input {
  flex: 1;
  background-color: #f8f8f8;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.container.dark .comment-input {
  background-color: #333;
  color: #fff;
}

.comment-submit {
  background-color: #2E7D32;
  color: white;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  border: none;
}

.comment-submit[disabled] {
  background-color: #ccc;
}

.comment-submit::after {
  border: none;
}

.comment-list {
  margin-top: 20rpx;
}

.comment-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.comment-name {
  font-size: 26rpx;
  font-weight: bold;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.comment-actions {
  display: flex;
  align-items: center;
}

.comment-action {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  font-size: 24rpx;
  color: #999;
}

.comment-action image {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.empty-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx;
  color: #999;
}

.empty-comments image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

/* 设置弹窗 */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.settings-content {
  width: 100%;
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.container.dark .settings-content {
  background-color: #2a2a2a;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.settings-title {
  font-size: 32rpx;
  font-weight: bold;
}

.settings-close {
  width: 40rpx;
  height: 40rpx;
}

.setting-section {
  margin-bottom: 40rpx;
}

.setting-label {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}

/* 主题选项 */
.theme-options {
  display: flex;
  justify-content: space-around;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
}

.theme-option.active {
  border-color: #2E7D32;
}

.theme-preview {
  width: 80rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.light-theme {
  background-color: #ffffff;
  border: 1rpx solid #ddd;
}

.dark-theme {
  background-color: #1a1a1a;
}

.sepia-theme {
  background-color: #f7f3e9;
}

.theme-option text {
  font-size: 24rpx;
}

/* 字体大小控制 */
.font-size-control,
.line-height-control {
  display: flex;
  align-items: center;
  justify-content: center;
}

.size-btn {
  width: 80rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container.dark .size-btn {
  background-color: #444;
  color: #fff;
}

.size-btn::after {
  border: none;
}

.size-display {
  margin: 0 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  min-width: 100rpx;
  text-align: center;
}

/* 字体选项 */
.font-options {
  display: flex;
  justify-content: space-around;
}

.font-option {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  background-color: #f5f5f5;
}

.container.dark .font-option {
  background-color: #444;
}

.font-option.active {
  border-color: #2E7D32;
  background-color: #e8f5e8;
}

.container.dark .font-option.active {
  background-color: #2E7D32;
}

.font-option text {
  font-size: 26rpx;
}
