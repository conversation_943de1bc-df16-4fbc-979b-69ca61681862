<view class="container">

  <view class="player-panel">
    <view class="player-name">{{player.name}}</view>
    <view class="player-level">{{player.levelName}}</view>

    <view class="health-bar-container">
      <text>生命:</text>
      <progress percent="{{(player.health / player.max_health) * 100}}" stroke-width="10" activeColor="#00c400" backgroundColor="#555" active="true" />
      <view class="health-text">{{player.health}} / {{player.max_health}}</view>
    </view>
    
    <view class="mana-bar-container">
      <text>法力:</text>
      <progress percent="{{(player.mana / player.max_mana) * 100}}" stroke-width="10" activeColor="#0077ff" backgroundColor="#555" active="true" />
      <view class="mana-text">{{player.mana}} / {{player.max_mana}}</view>
    </view>
    
    <view class="exp-bar-container">
      <text>灵力:</text>
      <progress percent="{{(player.exp / player.exp_needed) * 100}}" stroke-width="12" activeColor="#ffd700" backgroundColor="#555" active="true" />
      <view class="exp-text">{{player.exp}} / {{player.exp_needed}}</view>
    </view>
  </view>

  <view class="monster-panel" wx:if="{{monster && monster.health > 0}}">
    <view class="monster-name">{{monster.name}}</view>
    <view class="health-bar-container">
      <text>生命:</text>
      <progress percent="{{(monster.health / monster.max_health) * 100}}" stroke-width="10" activeColor="#c40000" backgroundColor="#555" active="true" />
      <view class="health-text">{{monster.health}} / {{monster.max_health}}</view>
    </view>
  </view>

  <view class="log-panel">
    <block wx:for="{{log}}" wx:key="index">
      <view class="log-item" style="color: {{item.color}}">{{item.text}}</view>
    </block>
  </view>

  <view class="action-panel">
    <view class="button-grid">
      <block wx:for="{{menu}}" wx:key="id">
        <button 
          class="action-button" 
          style="background-color:{{item.color}}"
          bindtap="onMenuTap" 
          data-id="{{item.id}}"
          disabled="{{item.disabled}}"
        >{{item.name}}</button>
      </block>
    </view>
  </view>
</view>