<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-box" bindtap="goToSearch">
      <image class="search-icon" src="/images/search.png"></image>
      <text class="search-placeholder">搜索文章...</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-id="{{item.articleId}}"></image>
        <view class="banner-content">
          <text class="banner-title">{{item.title}}</text>
          <text class="banner-desc">{{item.description}}</text>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 分类导航 -->
  <view class="category-section">
    <view class="section-title">
      <text class="title-text">文章分类</text>
      <text class="more-text" bindtap="goToCategory">更多 ></text>
    </view>
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
      <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="goToCategoryDetail" data-id="{{item.id}}">
        <view class="category-icon" style="background-color: {{item.color}}">
          <image src="/images/{{item.icon}}.png"></image>
        </view>
        <text class="category-name">{{item.name}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 热门文章 -->
  <view class="hot-section">
    <view class="section-title">
      <text class="title-text">热门文章</text>
      <text class="more-text" bindtap="goToHot">更多 ></text>
    </view>
    <scroll-view class="hot-scroll" scroll-x="true" show-scrollbar="false">
      <view class="hot-item" wx:for="{{hotArticles}}" wx:key="id" bindtap="goToArticle" data-id="{{item.id}}">
        <image class="hot-image" src="{{item.cover}}" mode="aspectFill"></image>
        <view class="hot-info">
          <text class="hot-title">{{item.title}}</text>
          <view class="hot-meta">
            <text class="hot-author">{{item.author}}</text>
            <text class="hot-views">{{item.views}}阅读</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 最新文章 -->
  <view class="article-section">
    <view class="section-title">
      <text class="title-text">最新文章</text>
    </view>
    <view class="article-list">
      <view class="article-item" wx:for="{{articles}}" wx:key="id" bindtap="goToArticle" data-id="{{item.id}}">
        <image class="article-cover" src="{{item.cover}}" mode="aspectFill"></image>
        <view class="article-content">
          <text class="article-title">{{item.title}}</text>
          <text class="article-summary">{{item.summary}}</text>
          <view class="article-meta">
            <text class="article-category">{{item.categoryName}}</text>
            <text class="article-author">{{item.author}}</text>
            <text class="article-time">{{item.publishTime}}</text>
            <text class="article-views">{{item.views}}阅读</text>
          </view>
          <view class="article-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
        <view class="article-action">
          <button class="read-btn" bindtap="readArticle" data-id="{{item.id}}" catchtap="true">
            <image src="/images/read.png"></image>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
    <text wx:if="{{!loading}}">点击加载更多</text>
    <text wx:else>加载中...</text>
  </view>
</view>
