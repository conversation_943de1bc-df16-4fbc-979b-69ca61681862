<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-box" bindtap="goToSearch">
      <image class="search-icon" src="/images/search.png"></image>
      <text class="search-placeholder">搜索资料...</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-url="{{item.url}}"></image>
      </swiper-item>
    </swiper>
  </view>

  <!-- 分类导航 -->
  <view class="category-section">
    <view class="section-title">
      <text class="title-text">资料分类</text>
      <text class="more-text" bindtap="goToCategory">更多 ></text>
    </view>
    <view class="category-grid">
      <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="goToCategoryDetail" data-id="{{item.id}}">
        <view class="category-icon" style="background-color: {{item.color}}">
          <image src="/images/{{item.icon}}.png"></image>
        </view>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 热门推荐 -->
  <view class="recommend-section">
    <view class="section-title">
      <text class="title-text">热门推荐</text>
      <text class="more-text" bindtap="goToRecommend">更多 ></text>
    </view>
    <scroll-view class="recommend-scroll" scroll-x="true" show-scrollbar="false">
      <view class="recommend-item" wx:for="{{hotResources}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <image class="recommend-image" src="{{item.cover}}" mode="aspectFill"></image>
        <view class="recommend-info">
          <text class="recommend-title">{{item.title}}</text>
          <view class="recommend-meta">
            <text class="recommend-size">{{item.size}}</text>
            <text class="recommend-downloads">{{item.downloads}}下载</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 最新资源 -->
  <view class="latest-section">
    <view class="section-title">
      <text class="title-text">最新资源</text>
    </view>
    <view class="latest-list">
      <view class="latest-item" wx:for="{{latestResources}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <image class="latest-cover" src="{{item.cover}}" mode="aspectFill"></image>
        <view class="latest-content">
          <text class="latest-title">{{item.title}}</text>
          <text class="latest-desc">{{item.description}}</text>
          <view class="latest-meta">
            <text class="latest-category">{{item.categoryName}}</text>
            <text class="latest-time">{{item.createTime}}</text>
            <text class="latest-size">{{item.size}}</text>
          </view>
        </view>
        <view class="latest-action">
          <button class="download-btn" bindtap="downloadResource" data-id="{{item.id}}" catchtap="true">
            <image src="/images/download.png"></image>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
    <text wx:if="{{!loading}}">点击加载更多</text>
    <text wx:else>加载中...</text>
  </view>
</view>
